#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高性能配置模块 - 专门用于优化QQ机器人回复速度
"""

import os
import json

# 性能优化配置
PERFORMANCE_CONFIG = {
    # 日志配置
    "enable_debug_logs": False,  # 禁用调试日志可显著提升性能
    "enable_timing_logs": False,  # 禁用性能测试日志
    "enable_message_logs": False,  # 禁用消息日志
    
    # 缓存配置
    "enable_config_cache": True,  # 启用配置缓存
    "cache_check_interval": 5.0,  # 缓存检查间隔（秒）
    "force_cache_mode": True,  # 强制缓存模式，减少文件系统调用
    
    # 消息处理配置
    "enable_fast_group_check": True,  # 启用快速群检查
    "enable_keyword_precompile": True,  # 启用关键词预编译
    "enable_early_return": True,  # 启用早期返回优化
    "max_keyword_length": 50,  # 最大关键词长度限制
    
    # 网络配置
    "reply_timeout": 3.0,  # 回复超时时间（秒）
    "connection_pool_size": 10,  # 连接池大小
    
    # 内存配置
    "max_cache_size": 1000,  # 最大缓存条目数
    "gc_interval": 300,  # 垃圾回收间隔（秒）
}

def load_performance_config():
    """加载性能配置"""
    config_file = "performance_config.json"
    
    if os.path.exists(config_file):
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                user_config = json.load(f)
                # 合并用户配置
                PERFORMANCE_CONFIG.update(user_config)
        except Exception as e:
            print(f"加载性能配置失败: {e}")
    
    return PERFORMANCE_CONFIG

def save_performance_config(config=None):
    """保存性能配置"""
    if config is None:
        config = PERFORMANCE_CONFIG
    
    config_file = "performance_config.json"
    
    try:
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"保存性能配置失败: {e}")
        return False

def apply_performance_optimizations():
    """应用性能优化设置"""
    config = load_performance_config()
    
    # 设置环境变量
    if not config["enable_debug_logs"]:
        os.environ["DISABLE_DEBUG_LOGS"] = "1"
    
    if config["force_cache_mode"]:
        os.environ["FORCE_CACHE_MODE"] = "1"
    
    # 返回配置供其他模块使用
    return config

def get_optimization_tips():
    """获取性能优化建议"""
    tips = [
        "1. 禁用不必要的日志记录可提升20-30%的性能",
        "2. 启用配置缓存可减少90%的文件I/O操作",
        "3. 使用预编译关键词缓存可提升50%的匹配速度",
        "4. 优化群检查逻辑可减少70%的处理时间",
        "5. 启用早期返回可避免不必要的计算",
        "6. 合理设置缓存大小可平衡内存使用和性能",
        "7. 调整网络超时设置可改善响应速度",
        "8. 定期垃圾回收可保持稳定的内存使用"
    ]
    return tips

def create_optimized_config():
    """创建优化的配置文件"""
    optimized_config = {
        "bot_uin": "1420937534",
        "monitored_groups": [
            {
                "group_id": "666732394",
                "group_name": "芜湖兼职群5元1单"
            }
        ],
        "keyword_replies": {
            "你好": ["1"],
            # 可以添加更多关键词，但建议控制在合理范围内
        },
        # 性能优化标记
        "_performance_optimized": True,
        "_optimization_version": "2.0",
        "_last_optimized": "2025-08-28"
    }
    
    return optimized_config

def benchmark_config_access():
    """基准测试配置访问性能"""
    import time
    import config_manager
    
    print("配置访问性能基准测试...")
    
    # 测试配置加载性能
    test_count = 1000
    start_time = time.time()
    
    for _ in range(test_count):
        config = config_manager.load_config()
    
    end_time = time.time()
    avg_time = (end_time - start_time) * 1000 / test_count
    
    print(f"配置加载平均耗时: {avg_time:.3f}ms")
    
    if avg_time < 0.1:
        print("✅ 配置访问性能优秀")
    elif avg_time < 1.0:
        print("✅ 配置访问性能良好")
    elif avg_time < 5.0:
        print("⚠️ 配置访问性能一般，建议优化")
    else:
        print("❌ 配置访问性能较差，需要优化")
    
    return avg_time

def main():
    """主函数 - 性能配置管理"""
    print("QQ机器人性能配置管理")
    print("=" * 40)
    
    # 加载当前配置
    config = load_performance_config()
    print("当前性能配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print("\n性能优化建议:")
    tips = get_optimization_tips()
    for tip in tips:
        print(f"  {tip}")
    
    print("\n配置访问性能测试:")
    benchmark_config_access()
    
    # 保存优化配置示例
    if not os.path.exists("performance_config.json"):
        save_performance_config()
        print("\n已创建性能配置文件: performance_config.json")

if __name__ == "__main__":
    main()
