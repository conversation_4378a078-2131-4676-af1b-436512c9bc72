[2025-08-28 11:11:18,042.042] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 11:11:18,045.045] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 11:11:18,045.045] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 11:11:18,046.046] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 11:11:18,048.048] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 11:11:18,048.048] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 11:11:18,049.049] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 11:11:18,050.050] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 11:11:18,051.051] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 11:11:18,052.052] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 11:11:18,164.164] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 11:11:18,297.297] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:11:18,464.464] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 11:11:18,830.830] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:11:19,282.282] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:11:19,309.309] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:11:19,345.345] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 11:11:23,509.509] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:11:27,603.603] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:3000
[2025-08-28 11:11:39,095.095] WARNING  [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:173) | 建议设置好 root 账号保证权限功能能够正常使用
[2025-08-28 11:11:39,096.096] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:174) | [BOTQQ]: 3292105955 | [WSURI]: ws://localhost:3001 | [WS_TOKEN]:  | [ROOT]: 123456 | [WEBUI]: http://localhost:6099
[2025-08-28 11:11:39,096.096] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:186) | 未启用插件白名单或黑名单，将加载所有插件
[2025-08-28 11:11:39,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:172) | 加载权限
[2025-08-28 11:11:39,097.097] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:178) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:11:39,097.097] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:183) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:11:39,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:11:43,193.193] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:ncatbot_service_remote_start:82) | NapCat 服务器离线, 启动本地 NapCat 服务中...
[2025-08-28 11:11:43,243.243] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.install (install.py:install_napcat_windows:68) | 未找到 napcat ，是否要自动安装？
[2025-08-28 11:12:44,006.006] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 11:12:44,635.635] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET /https://raw.githubusercontent.com/NapNeko/NapCatQQ/main/package.json HTTP/1.1" 200 889
[2025-08-28 11:12:44,635.635] DEBUG    [Thread-2 (<lambda>)|MainProcess] adapter.nc.install (install.py:get_napcat_version:47) | 获取最新版本信息成功, 版本号: 4.8.102
[2025-08-28 11:12:44,635.635] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.install (install.py:install_napcat_windows:83) | 下载链接为 https://proxy.yaoyaoling.net/https://github.com/NapNeko/NapCatQQ/releases/download/v4.8.102/NapCat.Shell.zip...
[2025-08-28 11:12:44,636.636] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.install (install.py:install_napcat_windows:84) | 正在下载 napcat 客户端, 请稍等...
[2025-08-28 11:12:44,637.637] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 11:12:45,402.402] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET /https://github.com/NapNeko/NapCatQQ/releases/download/v4.8.102/NapCat.Shell.zip HTTP/1.1" 200 20599224
[2025-08-28 11:14:54,119.119] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (file_io.py:unzip_file:22) | 解压 napcat.zip 成功
[2025-08-28 11:14:54,128.128] WARNING  [Thread-2 (<lambda>)|MainProcess] adapter.nc.start (start.py:read_webui_config:223) | 无法读取 WebUI 配置, 将使用默认配置
[2025-08-28 11:14:54,129.129] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.start (start.py:get_launcher_name:99) | 当前操作系统为: Windows 11
[2025-08-28 11:14:54,129.129] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.start (start.py:start_napcat_windows:112) | 正在启动QQ, 启动器路径: F:\soft\python\qq\napcat\NapCat.34740.Shell\napcat\launcher.bat
[2025-08-28 11:14:54,172.172] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:login:248) | 即将弹出权限请求, 请允许
[2025-08-28 11:14:54,193.193] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:14:58,300.300] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:15:02,419.419] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:15:06,507.507] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:15:10,623.623] ERROR    [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:__init__:97) | 授权操作超时
[2025-08-28 11:15:10,623.623] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:__init__:98) | 请检查 Windows 安全中心, 查看是否有拦截了 NapCat 启动程序的日志
[2025-08-28 11:17:42,107.107] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 11:17:42,109.109] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 11:17:42,110.110] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 11:17:42,112.112] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 11:17:42,114.114] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 11:17:42,114.114] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 11:17:42,115.115] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 11:17:42,116.116] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 11:17:42,116.116] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 11:17:42,117.117] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 11:17:42,293.293] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 11:17:42,293.293] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:17:42,439.439] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:17:42,499.499] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 11:17:42,862.862] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:17:42,912.912] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 11:17:43,117.117] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:17:44,709.709] WARNING  [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:173) | 建议设置好 root 账号保证权限功能能够正常使用
[2025-08-28 11:17:44,710.710] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:174) | [BOTQQ]: 3292105955 | [WSURI]: ws://localhost:3001 | [WS_TOKEN]:  | [ROOT]: 123456 | [WEBUI]: http://localhost:6099
[2025-08-28 11:17:44,710.710] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:186) | 未启用插件白名单或黑名单，将加载所有插件
[2025-08-28 11:17:44,710.710] DEBUG    [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:172) | 加载权限
[2025-08-28 11:17:44,710.710] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:178) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:17:44,711.711] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:183) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:17:44,711.711] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:17:44,866.866] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:17:48,792.792] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:ncatbot_service_remote_start:82) | NapCat 服务器离线, 启动本地 NapCat 服务中...
[2025-08-28 11:17:48,856.856] WARNING  [Thread-2 (<lambda>)|MainProcess] adapter.nc.start (start.py:read_webui_config:223) | 无法读取 WebUI 配置, 将使用默认配置
[2025-08-28 11:17:48,857.857] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.start (start.py:get_launcher_name:99) | 当前操作系统为: Windows 11
[2025-08-28 11:17:48,857.857] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.start (start.py:start_napcat_windows:112) | 正在启动QQ, 启动器路径: F:\soft\python\qq\napcat\NapCat.34740.Shell\napcat\launcher.bat
[2025-08-28 11:17:48,887.887] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:login:248) | 即将弹出权限请求, 请允许
[2025-08-28 11:17:48,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:17:53,032.032] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:17:57,118.118] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:18:01,227.227] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:18:05,339.339] ERROR    [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:__init__:97) | 授权操作超时
[2025-08-28 11:18:05,339.339] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:__init__:98) | 请检查 Windows 安全中心, 查看是否有拦截了 NapCat 启动程序的日志
[2025-08-28 11:21:10,206.206] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 11:21:10,208.208] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 11:21:10,208.208] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 11:21:10,209.209] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 11:21:10,210.210] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 11:21:10,210.210] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 11:21:10,210.210] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 11:21:10,212.212] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 11:21:10,212.212] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 11:21:10,214.214] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 11:21:10,390.390] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 11:21:10,391.391] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:21:10,568.568] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 11:21:10,951.951] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 11:21:10,966.966] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:21:10,984.984] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:21:11,061.061] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:21:11,123.123] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:21:15,062.062] WARNING  [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:173) | 建议设置好 root 账号保证权限功能能够正常使用
[2025-08-28 11:21:15,062.062] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:174) | [BOTQQ]: 3292105955 | [WSURI]: ws://localhost:3001 | [WS_TOKEN]:  | [ROOT]: 123456 | [WEBUI]: http://localhost:6099
[2025-08-28 11:21:15,062.062] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:186) | 未启用插件白名单或黑名单，将加载所有插件
[2025-08-28 11:21:15,063.063] DEBUG    [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:172) | 加载权限
[2025-08-28 11:21:15,063.063] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:178) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:21:15,063.063] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:183) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:21:15,064.064] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:21:19,143.143] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:ncatbot_service_remote_start:82) | NapCat 服务器离线, 启动本地 NapCat 服务中...
[2025-08-28 11:21:19,198.198] WARNING  [Thread-2 (<lambda>)|MainProcess] adapter.nc.start (start.py:read_webui_config:223) | 无法读取 WebUI 配置, 将使用默认配置
[2025-08-28 11:21:19,198.198] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.start (start.py:get_launcher_name:99) | 当前操作系统为: Windows 11
[2025-08-28 11:21:19,199.199] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.start (start.py:start_napcat_windows:112) | 正在启动QQ, 启动器路径: F:\soft\python\qq\napcat\NapCat.34740.Shell\napcat\launcher.bat
[2025-08-28 11:21:19,228.228] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:login:248) | 即将弹出权限请求, 请允许
[2025-08-28 11:21:19,249.249] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:21:21,327.327] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/auth/login HTTP/1.1" 200 315
[2025-08-28 11:21:21,349.349] DEBUG    [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:__init__:63) | 成功连接到 WEBUI
[2025-08-28 11:21:21,349.349] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:21:23,401.401] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/QQLogin/GetQQLoginInfo HTTP/1.1" 200 124
[2025-08-28 11:21:23,401.401] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:login:242) | napcat 已登录成功
[2025-08-28 11:21:23,402.402] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:connect_napcat:39) | 正在连接 napcat websocket 服务器...
[2025-08-28 11:21:23,402.402] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:21:27,516.516] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:21:31,610.610] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:21:35,728.728] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:21:39,380.380] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:3000
[2025-08-28 11:21:39,835.835] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:21:43,980.980] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:21:48,104.104] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:21:52,226.226] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:21:56,352.352] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:22:00,464.464] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:22:04,589.589] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:22:08,738.738] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:22:12,872.872] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:22:17,026.026] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:22:21,180.180] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:22:25,313.313] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:info:26) | 连接 napcat websocket 服务器超时, 请检查以下内容:
[2025-08-28 11:22:25,313.313] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:info_windows:22) | ===请允许终端修改计算机, 并在弹出的另一个终端扫码登录===
[2025-08-28 11:22:25,313.313] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:info_windows:23) | ===确认 bot QQ 号 3292105955 是否正确===
[2025-08-28 11:22:25,313.313] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:info:33) | ===websocket url 是否正确: ws://localhost:3001===
[2025-08-28 11:24:23,644.644] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 11:24:23,646.646] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 11:24:23,647.647] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 11:24:23,647.647] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 11:24:23,649.649] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 11:24:23,650.650] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 11:24:23,652.652] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 11:24:23,652.652] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 11:24:23,653.653] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 11:24:23,653.653] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 11:24:23,772.772] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 11:24:23,822.822] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:24:23,833.833] WARNING  [MainThread|MainProcess] Logger (config.py:validate_config:173) | 建议设置好 root 账号保证权限功能能够正常使用
[2025-08-28 11:24:23,833.833] INFO     [MainThread|MainProcess] Logger (config.py:validate_config:174) | [BOTQQ]: 3292105955 | [WSURI]: ws://localhost:3001 | [WS_TOKEN]:  | [ROOT]: 123456 | [WEBUI]: http://localhost:6099
[2025-08-28 11:24:23,833.833] INFO     [MainThread|MainProcess] Logger (config.py:validate_config:186) | 未启用插件白名单或黑名单，将加载所有插件
[2025-08-28 11:24:23,834.834] DEBUG    [MainThread|MainProcess] AccessController (access_controller.py:_load_access:172) | 加载权限
[2025-08-28 11:24:23,834.834] WARNING  [MainThread|MainProcess] AccessController (access_controller.py:_load_access:178) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:24:23,834.834] WARNING  [MainThread|MainProcess] AccessController (access_controller.py:_load_access:183) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:24:23,835.835] DEBUG    [MainThread|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:24:23,840.840] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 11:24:23,841.841] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:114) | > GET / HTTP/1.1
[2025-08-28 11:24:23,842.842] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 11:24:23,842.842] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 11:24:23,842.842] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 11:24:23,842.842] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: FmQEP1nHWJJHfq87isULhg==
[2025-08-28 11:24:23,842.842] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 11:24:23,842.842] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 11:24:23,842.842] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 11:24:23,842.842] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 11:24:23,844.844] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 11:24:23,844.844] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 11:24:23,844.844] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 11:24:23,844.844] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: GU4q+/sQNFLnAE2+BVPwHQjRxPs=
[2025-08-28 11:24:23,844.844] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 11:24:23,844.844] DEBUG    [MainThread|MainProcess] Logger (wsroute.py:check_websocket:29) | WebSocket ws://localhost:3001 可用.
[2025-08-28 11:24:23,844.844] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 11:24:23,845.845] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 11:24:23,845.845] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351463,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 11:24:23,847.847] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 11:24:23,847.847] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 11:24:23,849.849] INFO     [MainThread|MainProcess] adapter.nc.launcher (launcher.py:ncatbot_service_remote_start:51) | napcat 服务器 ws://localhost:3001 在线, 连接中...
[2025-08-28 11:24:23,872.872] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:24:24,024.024] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 11:24:24,385.385] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 11:24:24,424.424] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:24:24,622.622] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:24:24,682.682] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:24:24,784.784] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:24:25,931.931] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/auth/login HTTP/1.1" 200 315
[2025-08-28 11:24:25,952.952] DEBUG    [MainThread|MainProcess] adapter.nc.login (login.py:__init__:63) | 成功连接到 WEBUI
[2025-08-28 11:24:25,953.953] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:24:27,973.973] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/QQLogin/GetQQLoginInfo HTTP/1.1" 200 124
[2025-08-28 11:24:27,973.973] INFO     [MainThread|MainProcess] Logger (client.py:run:319) | NapCat 服务启动登录完成
[2025-08-28 11:24:27,974.974] DEBUG    [MainThread|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:24:27,974.974] DEBUG    [MainThread|MainProcess] Logger (client.py:info_subscribe_message_types:250) | 已订阅消息类型:[群聊]全部消息类型
[2025-08-28 11:24:27,975.975] DEBUG    [MainThread|MainProcess] Logger (client.py:info_subscribe_message_types:251) | 已订阅消息类型:[私聊]全部消息类型
[2025-08-28 11:24:27,975.975] INFO     [MainThread|MainProcess] PluginLoader (loader.py:load_plugins:474) | 插件目录: F:\soft\python\qq\napcat\NapCat.34740.Shell\plugins 不存在......跳过加载插件
[2025-08-28 11:24:27,977.977] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 11:24:27,978.978] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /event HTTP/1.1
[2025-08-28 11:24:27,978.978] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 11:24:27,978.978] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 11:24:27,978.978] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 11:24:27,978.978] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: ASbmyPGEwZATfnR9BBWymg==
[2025-08-28 11:24:27,978.978] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 11:24:27,978.978] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 11:24:27,978.978] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 11:24:27,978.978] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 11:24:27,979.979] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 11:24:27,979.979] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 11:24:27,979.979] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 11:24:27,979.979] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: 8hxqUy/lnEnB55GSGfZJ/7SytEg=
[2025-08-28 11:24:27,979.979] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 11:24:27,979.979] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351467,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 11:24:27,979.979] INFO     [MainThread|MainProcess] Logger (connect.py:on_message:43) | 机器人 3292105955 成功启动
[2025-08-28 11:24:54,706.706] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351494,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:24:54,707.707] DEBUG    [MainThread|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351494, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:25:24,719.719] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351524,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:25:24,719.719] DEBUG    [MainThread|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351524, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:25:54,724.724] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351554,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:25:54,724.724] DEBUG    [MainThread|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351554, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:26:24,733.733] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351584,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:26:24,733.733] DEBUG    [MainThread|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351584, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:26:48,937.937] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 11:26:48,939.939] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 11:26:48,939.939] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 11:26:48,940.940] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 11:26:48,941.941] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 11:26:48,942.942] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 11:26:48,943.943] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 11:26:48,944.944] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 11:26:48,945.945] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 11:26:48,945.945] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 11:26:49,056.056] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 11:26:49,253.253] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:26:49,338.338] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 11:26:49,713.713] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 11:26:49,749.749] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:26:49,773.773] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:26:49,833.833] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:26:49,924.924] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:27:11,091.091] WARNING  [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:173) | 建议设置好 root 账号保证权限功能能够正常使用
[2025-08-28 11:27:11,091.091] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:174) | [BOTQQ]: 3292105955 | [WSURI]: ws://localhost:3001 | [WS_TOKEN]:  | [ROOT]: 123456 | [WEBUI]: http://localhost:6099
[2025-08-28 11:27:11,091.091] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:186) | 未启用插件白名单或黑名单，将加载所有插件
[2025-08-28 11:27:11,092.092] DEBUG    [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:172) | 加载权限
[2025-08-28 11:27:11,092.092] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:178) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:27:11,092.092] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:183) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:27:11,093.093] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:27:11,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 11:27:11,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET / HTTP/1.1
[2025-08-28 11:27:11,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 11:27:11,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 11:27:11,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 11:27:11,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: iSRxqmKuP/ZVzHjWK6XUBQ==
[2025-08-28 11:27:11,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 11:27:11,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 11:27:11,098.098] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 11:27:11,098.098] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 11:27:11,098.098] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 11:27:11,098.098] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 11:27:11,098.098] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 11:27:11,098.098] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: Cxrmk2558VChC9fFwrb+eueBTPk=
[2025-08-28 11:27:11,098.098] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 11:27:11,099.099] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (wsroute.py:check_websocket:29) | WebSocket ws://localhost:3001 可用.
[2025-08-28 11:27:11,099.099] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 11:27:11,099.099] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 11:27:11,099.099] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351631,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 11:27:11,099.099] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 11:27:11,099.099] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 11:27:11,100.100] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:ncatbot_service_remote_start:51) | napcat 服务器 ws://localhost:3001 在线, 连接中...
[2025-08-28 11:27:11,122.122] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:27:13,183.183] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/auth/login HTTP/1.1" 200 315
[2025-08-28 11:27:13,205.205] DEBUG    [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:__init__:63) | 成功连接到 WEBUI
[2025-08-28 11:27:13,205.205] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:27:15,279.279] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/QQLogin/GetQQLoginInfo HTTP/1.1" 200 124
[2025-08-28 11:27:15,279.279] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (client.py:run:319) | NapCat 服务启动登录完成
[2025-08-28 11:27:15,280.280] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:27:15,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:info_subscribe_message_types:250) | 已订阅消息类型:[群聊]全部消息类型
[2025-08-28 11:27:15,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:info_subscribe_message_types:251) | 已订阅消息类型:[私聊]全部消息类型
[2025-08-28 11:27:15,281.281] INFO     [Thread-2 (<lambda>)|MainProcess] PluginLoader (loader.py:load_plugins:474) | 插件目录: F:\soft\python\qq\napcat\NapCat.34740.Shell\plugins 不存在......跳过加载插件
[2025-08-28 11:27:15,282.282] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 11:27:15,283.283] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /event HTTP/1.1
[2025-08-28 11:27:15,283.283] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 11:27:15,283.283] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 11:27:15,283.283] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 11:27:15,283.283] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: AvhjJWjECOdX5beCejD37A==
[2025-08-28 11:27:15,283.283] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 11:27:15,283.283] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 11:27:15,283.283] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 11:27:15,283.283] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 11:27:15,283.283] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 11:27:15,284.284] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 11:27:15,284.284] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 11:27:15,284.284] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: pJRpQGgZpUw3Pld117B01sUXj+U=
[2025-08-28 11:27:15,284.284] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 11:27:15,284.284] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351635,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 11:27:15,284.284] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:43) | 机器人 3292105955 成功启动
[2025-08-28 11:27:24,753.753] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351644,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:27:24,753.753] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351644, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:27:54,768.768] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351674,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:27:54,768.768] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351674, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:28:03,772.772] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:3000
[2025-08-28 11:28:24,774.774] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351704,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:28:24,774.774] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351704, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:28:54,784.784] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351734,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:28:54,784.784] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351734, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:29:01,933.933] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:3000
[2025-08-28 11:29:04,001.001] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:3000 "GET /get_group_list HTTP/1.1" 200 7139
[2025-08-28 11:29:24,797.797] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351764,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:29:24,797.797] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351764, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:29:54,798.798] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351794,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:29:54,798.798] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351794, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:30:24,814.814] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351824,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:30:24,815.815] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351824, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:30:37,864.864] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351837,"self_id":3292105955,"post_t...420937534,"group_id":0}' [197 bytes]
[2025-08-28 11:30:37,864.864] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756351837, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '对方正在输入...', 'event_type': 1, 'user_id': 1420937534, 'group_id': 0}
[2025-08-28 11:30:38,387.387] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351838,"self_id":3292105955,"post_t...420937534,"group_id":0}' [176 bytes]
[2025-08-28 11:30:38,387.387] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756351838, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '', 'event_type': 2, 'user_id': 1420937534, 'group_id': 0}
[2025-08-28 11:30:38,444.444] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351838,"self_id":3292105955,"post_t...420937534,"group_id":0}' [176 bytes]
[2025-08-28 11:30:38,445.445] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756351838, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '', 'event_type': 2, 'user_id': 1420937534, 'group_id': 0}
[2025-08-28 11:30:38,668.668] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":1420937534,"tim..."target_id":1420937534}' [404 bytes]
[2025-08-28 11:30:38,668.668] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_private_event:182) | {'message_id': '631370638', 'user_id': '1420937534', 'message_seq': '631370638', 'real_id': '631370638', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'font': '14', 'sub_type': 'friend', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array', 'target_id': '1420937534', 'message_type': 'private'}
[2025-08-28 11:30:38,668.668] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_private_message:55) | {'message_id': '631370638', 'user_id': '1420937534', 'message_seq': '631370638', 'real_id': '631370638', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'font': '14', 'sub_type': 'friend', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array', 'target_id': '1420937534', 'message_type': 'private'}
[2025-08-28 11:30:42,874.874] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351842,"self_id":3292105955,"post_t...420937534,"group_id":0}' [197 bytes]
[2025-08-28 11:30:42,874.874] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756351842, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '对方正在输入...', 'event_type': 2, 'user_id': 1420937534, 'group_id': 0}
[2025-08-28 11:30:50,866.866] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351850,"self_id":3292105955,"post_t...420937534,"group_id":0}' [176 bytes]
[2025-08-28 11:30:50,866.866] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756351850, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '', 'event_type': 2, 'user_id': 1420937534, 'group_id': 0}
[2025-08-28 11:30:54,825.825] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351854,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:30:54,826.826] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351854, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:31:24,849.849] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351884,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:31:24,850.850] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351884, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:31:54,856.856] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351914,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:31:54,856.856] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351914, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:32:24,866.866] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351944,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:32:24,867.867] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351944, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:32:54,883.883] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756351974,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:32:54,883.883] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756351974, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:33:24,887.887] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352004,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:33:24,887.887] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352004, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:33:54,902.902] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352034,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:33:54,902.902] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352034, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:34:24,905.905] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352064,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:34:24,905.905] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352064, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:34:54,919.919] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352094,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:34:54,920.920] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352094, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:35:24,924.924] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352124,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:35:24,924.924] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352124, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:35:54,931.931] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352154,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:35:54,932.932] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352154, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:36:12,960.960] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352172,"self_id":3292105955,"post_t...55,"sub_type":"invite"}' [179 bytes]
[2025-08-28 11:36:12,961.961] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756352172, 'self_id': 3292105955, 'post_type': 'notice', 'group_id': 666732394, 'user_id': 1420937534, 'notice_type': 'group_increase', 'operator_id': 3292105955, 'sub_type': 'invite'}
[2025-08-28 11:36:24,934.934] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352184,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:36:24,934.934] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352184, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:36:54,945.945] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352214,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:36:54,945.945] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352214, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:37:24,949.949] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352244,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:37:24,949.949] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352244, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:37:29,295.295] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352249,"self_id":3292105955,"post_t...420937534,"group_id":0}' [197 bytes]
[2025-08-28 11:37:29,298.298] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756352249, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '对方正在输入...', 'event_type': 1, 'user_id': 1420937534, 'group_id': 0}
[2025-08-28 11:37:29,716.716] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352249,"self_id":3292105955,"post_t...420937534,"group_id":0}' [176 bytes]
[2025-08-28 11:37:29,716.716] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756352249, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '', 'event_type': 2, 'user_id': 1420937534, 'group_id': 0}
[2025-08-28 11:37:29,783.783] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352249,"self_id":3292105955,"post_t...420937534,"group_id":0}' [176 bytes]
[2025-08-28 11:37:29,783.783] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756352249, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '', 'event_type': 2, 'user_id': 1420937534, 'group_id': 0}
[2025-08-28 11:37:29,930.930] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":1420937534,"tim..."target_id":1420937534}' [407 bytes]
[2025-08-28 11:37:29,930.930] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_private_event:182) | {'message_id': '1928728396', 'user_id': '1420937534', 'message_seq': '1928728396', 'real_id': '1928728396', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'font': '14', 'sub_type': 'friend', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array', 'target_id': '1420937534', 'message_type': 'private'}
[2025-08-28 11:37:29,930.930] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_private_message:55) | {'message_id': '1928728396', 'user_id': '1420937534', 'message_seq': '1928728396', 'real_id': '1928728396', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'font': '14', 'sub_type': 'friend', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array', 'target_id': '1420937534', 'message_type': 'private'}
[2025-08-28 11:37:34,297.297] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352254,"self_id":3292105955,"post_t...420937534,"group_id":0}' [197 bytes]
[2025-08-28 11:37:34,297.297] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756352254, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '对方正在输入...', 'event_type': 2, 'user_id': 1420937534, 'group_id': 0}
[2025-08-28 11:37:54,962.962] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352274,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:37:54,962.962] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352274, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:37:55,391.391] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352275,"self_id":3292105955,"post_t...420937534,"group_id":0}' [176 bytes]
[2025-08-28 11:37:55,392.392] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756352275, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '', 'event_type': 2, 'user_id': 1420937534, 'group_id': 0}
[2025-08-28 11:38:24,975.975] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352304,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:38:24,976.976] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352304, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:38:54,991.991] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352334,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:38:54,991.991] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352334, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:39:25,004.004] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352365,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:39:25,005.005] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352365, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:39:55,007.007] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352395,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:39:55,007.007] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352395, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:40:25,010.010] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352425,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:40:25,010.010] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352425, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:40:55,024.024] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352455,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:40:55,024.024] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352455, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:41:25,039.039] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352485,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:41:25,039.039] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352485, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:41:55,047.047] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352515,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:41:55,047.047] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352515, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:42:17,856.856] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 11:42:17,858.858] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 11:42:17,859.859] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 11:42:17,860.860] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 11:42:17,861.861] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 11:42:17,862.862] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 11:42:17,862.862] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 11:42:17,863.863] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 11:42:17,864.864] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 11:42:17,865.865] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 11:42:18,030.030] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 11:42:18,088.088] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:42:18,239.239] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 11:42:18,559.559] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:42:18,603.603] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:42:18,617.617] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 11:42:18,645.645] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:42:18,863.863] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 11:42:21,286.286] WARNING  [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:173) | 建议设置好 root 账号保证权限功能能够正常使用
[2025-08-28 11:42:21,286.286] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:174) | [BOTQQ]: 3292105955 | [WSURI]: ws://localhost:3001 | [WS_TOKEN]:  | [ROOT]: 123456 | [WEBUI]: http://localhost:6099
[2025-08-28 11:42:21,286.286] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:186) | 未启用插件白名单或黑名单，将加载所有插件
[2025-08-28 11:42:21,286.286] DEBUG    [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:172) | 加载权限
[2025-08-28 11:42:21,287.287] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:178) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:42:21,287.287] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:183) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 11:42:21,288.288] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:42:21,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 11:42:21,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET / HTTP/1.1
[2025-08-28 11:42:21,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 11:42:21,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 11:42:21,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 11:42:21,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: 0hApUoo7pr77iGmnR+Z3bA==
[2025-08-28 11:42:21,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 11:42:21,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 11:42:21,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 11:42:21,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 11:42:21,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 11:42:21,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 11:42:21,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 11:42:21,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: WGkeKAmHb2JNLqRvV+Oj8kPvsic=
[2025-08-28 11:42:21,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 11:42:21,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (wsroute.py:check_websocket:29) | WebSocket ws://localhost:3001 可用.
[2025-08-28 11:42:21,293.293] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 11:42:21,293.293] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 11:42:21,293.293] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352541,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 11:42:21,293.293] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 11:42:21,293.293] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 11:42:21,294.294] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:ncatbot_service_remote_start:51) | napcat 服务器 ws://localhost:3001 在线, 连接中...
[2025-08-28 11:42:21,316.316] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:42:23,363.363] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/auth/login HTTP/1.1" 200 315
[2025-08-28 11:42:23,384.384] DEBUG    [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:__init__:63) | 成功连接到 WEBUI
[2025-08-28 11:42:23,385.385] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 11:42:25,449.449] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/QQLogin/GetQQLoginInfo HTTP/1.1" 200 124
[2025-08-28 11:42:25,450.450] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (client.py:run:319) | NapCat 服务启动登录完成
[2025-08-28 11:42:25,450.450] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 11:42:25,451.451] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:info_subscribe_message_types:250) | 已订阅消息类型:[群聊]全部消息类型
[2025-08-28 11:42:25,451.451] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:info_subscribe_message_types:251) | 已订阅消息类型:[私聊]全部消息类型
[2025-08-28 11:42:25,451.451] INFO     [Thread-2 (<lambda>)|MainProcess] PluginLoader (loader.py:load_plugins:474) | 插件目录: F:\soft\python\qq\napcat\NapCat.34740.Shell\plugins 不存在......跳过加载插件
[2025-08-28 11:42:25,453.453] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 11:42:25,453.453] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /event HTTP/1.1
[2025-08-28 11:42:25,453.453] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 11:42:25,453.453] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 11:42:25,453.453] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 11:42:25,453.453] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: 9CVT+iDnETq2Owf1wImBuA==
[2025-08-28 11:42:25,453.453] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 11:42:25,454.454] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 11:42:25,454.454] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 11:42:25,454.454] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 11:42:25,454.454] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 11:42:25,454.454] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 11:42:25,454.454] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 11:42:25,454.454] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: E27PCew2I96XKwuNJ9q0bGc3cFk=
[2025-08-28 11:42:25,454.454] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 11:42:25,454.454] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352545,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 11:42:25,454.454] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:43) | 机器人 3292105955 成功启动
[2025-08-28 11:42:40,708.708] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:3000
[2025-08-28 11:42:42,730.730] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:3000 "GET /get_group_list HTTP/1.1" 200 7139
[2025-08-28 11:42:55,066.066] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352575,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:42:55,067.067] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352575, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:43:25,083.083] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352605,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:43:25,083.083] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352605, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:43:44,490.490] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":1420937534,"tim...roup_name":"芜湖兼职群5元1单"}' [458 bytes]
[2025-08-28 11:43:44,490.490] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'message_id': '925373241', 'message_seq': '925373241', 'real_id': '925373241', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 11:43:44,490.490] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:27) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'message_id': '925373241', 'message_seq': '925373241', 'real_id': '925373241', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 11:43:44,494.494] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 11:43:44,494.494] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 11:43:44,494.494] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: KVFmVRhEdVrsVCoed4dBTA==
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 11:43:44,495.495] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: kNPUtouol9v/5JjPdDvZhBRfV/0=
[2025-08-28 11:43:44,496.496] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 11:43:44,496.496] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756352624}' [202 bytes]
[2025-08-28 11:43:44,861.861] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756352624}' [104 bytes]
[2025-08-28 11:43:44,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 11:43:44,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 11:43:44,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 11:43:44,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 11:43:44,863.863] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1952538013}, 'message': '', 'wording': '', 'echo': 1756352624}
[2025-08-28 11:43:44,863.863] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:50) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 11:43:55,097.097] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352635,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:43:55,098.098] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352635, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:44:25,107.107] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352665,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:44:25,108.108] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352665, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:44:55,123.123] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352695,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:44:55,123.123] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352695, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:45:25,135.135] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352725,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:45:25,136.136] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352725, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 11:45:55,148.148] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756352755,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 11:45:55,149.149] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756352755, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:25:17,772.772] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 13:25:17,775.775] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 13:25:17,776.776] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 13:25:17,777.777] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 13:25:17,778.778] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 13:25:17,778.778] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 13:25:17,779.779] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 13:25:17,780.780] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 13:25:17,780.780] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 13:25:17,780.780] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 13:25:17,988.988] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 13:25:18,042.042] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:25:18,186.186] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 13:25:18,724.724] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:25:19,366.366] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:25:19,607.607] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:25:20,274.274] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:25:22,117.117] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 13:25:26,311.311] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 13:25:26,313.313] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 13:25:26,314.314] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 13:25:26,316.316] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 13:25:26,316.316] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 13:25:26,316.316] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 13:25:26,317.317] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 13:25:26,319.319] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 13:25:26,320.320] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 13:25:26,320.320] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 13:25:26,430.430] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 13:25:26,552.552] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:25:26,657.657] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 13:25:27,151.151] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:25:27,252.252] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:25:27,526.526] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:25:27,664.664] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:25:28,877.877] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 13:25:30,171.171] WARNING  [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:173) | 建议设置好 root 账号保证权限功能能够正常使用
[2025-08-28 13:25:30,171.171] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:174) | [BOTQQ]: 3292105955 | [WSURI]: ws://localhost:3001 | [WS_TOKEN]:  | [ROOT]: 123456 | [WEBUI]: http://localhost:6099
[2025-08-28 13:25:30,171.171] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:186) | 未启用插件白名单或黑名单，将加载所有插件
[2025-08-28 13:25:30,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:172) | 加载权限
[2025-08-28 13:25:30,172.172] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:178) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 13:25:30,172.172] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:183) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 13:25:30,173.173] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 13:25:30,178.178] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:25:30,179.179] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET / HTTP/1.1
[2025-08-28 13:25:30,180.180] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:25:30,180.180] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:25:30,180.180] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:25:30,180.180] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: WjB46qEkBKG6xScWFEqabA==
[2025-08-28 13:25:30,180.180] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:25:30,180.180] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:25:30,180.180] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:25:30,180.180] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:25:30,181.181] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:25:30,181.181] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:25:30,181.181] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:25:30,181.181] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: EBkU6TCoYvWS1DnQpqnNOBWk6ag=
[2025-08-28 13:25:30,182.182] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:25:30,182.182] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (wsroute.py:check_websocket:29) | WebSocket ws://localhost:3001 可用.
[2025-08-28 13:25:30,182.182] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:25:30,183.183] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:25:30,184.184] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358730,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 13:25:30,184.184] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:25:30,184.184] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:25:30,186.186] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:ncatbot_service_remote_start:51) | napcat 服务器 ws://localhost:3001 在线, 连接中...
[2025-08-28 13:25:30,208.208] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 13:25:32,242.242] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/auth/login HTTP/1.1" 200 315
[2025-08-28 13:25:32,264.264] DEBUG    [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:__init__:63) | 成功连接到 WEBUI
[2025-08-28 13:25:32,264.264] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 13:25:34,342.342] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/QQLogin/GetQQLoginInfo HTTP/1.1" 200 124
[2025-08-28 13:25:34,343.343] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (client.py:run:319) | NapCat 服务启动登录完成
[2025-08-28 13:25:34,343.343] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 13:25:34,344.344] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:info_subscribe_message_types:250) | 已订阅消息类型:[群聊]全部消息类型
[2025-08-28 13:25:34,344.344] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:info_subscribe_message_types:251) | 已订阅消息类型:[私聊]全部消息类型
[2025-08-28 13:25:34,344.344] INFO     [Thread-2 (<lambda>)|MainProcess] PluginLoader (loader.py:load_plugins:474) | 插件目录: F:\soft\python\qq\napcat\NapCat.34740.Shell\plugins 不存在......跳过加载插件
[2025-08-28 13:25:34,347.347] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:25:34,347.347] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /event HTTP/1.1
[2025-08-28 13:25:34,347.347] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:25:34,347.347] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:25:34,347.347] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:25:34,347.347] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: 9TYXzshGtK0R3yUXL3ATgQ==
[2025-08-28 13:25:34,347.347] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:25:34,348.348] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:25:34,348.348] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:25:34,348.348] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:25:34,348.348] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:25:34,348.348] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:25:34,348.348] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:25:34,348.348] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: EOSxAUky5qGdavSs97jA3OTsozw=
[2025-08-28 13:25:34,348.348] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:25:34,349.349] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358734,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 13:25:34,349.349] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:43) | 机器人 3292105955 成功启动
[2025-08-28 13:25:45,888.888] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358745,"self_id":3292105955,"post_t...397778636,"group_id":0}' [176 bytes]
[2025-08-28 13:25:45,888.888] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756358745, 'self_id': 3292105955, 'post_type': 'notice', 'notice_type': 'notify', 'sub_type': 'input_status', 'status_text': '', 'event_type': 2, 'user_id': 2397778636, 'group_id': 0}
[2025-08-28 13:25:56,773.773] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358756,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:25:56,773.773] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756358756, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:26:02,871.871] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:3000
[2025-08-28 13:26:04,919.919] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:3000 "GET /get_group_list HTTP/1.1" 200 7139
[2025-08-28 13:26:26,787.787] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358786,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:26:26,788.788] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756358786, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:26:45,857.857] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":1420937534,"tim...roup_name":"芜湖兼职群5元1单"}' [458 bytes]
[2025-08-28 13:26:45,859.859] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'message_id': '695132399', 'message_seq': '695132399', 'real_id': '695132399', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:26:45,859.859] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'message_id': '695132399', 'message_seq': '695132399', 'real_id': '695132399', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:26:45,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:26:45,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:26:45,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:26:45,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:26:45,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:26:45,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: 3l+BRGqiyu26ycGvHYFeJA==
[2025-08-28 13:26:45,862.862] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:26:45,863.863] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:26:45,863.863] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:26:45,863.863] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:26:45,863.863] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:26:45,863.863] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:26:45,863.863] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:26:45,863.863] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: ZbPQ6MIY2ETKkVeoQ4fQ6e2shqk=
[2025-08-28 13:26:45,863.863] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:26:45,864.864] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756358805}' [202 bytes]
[2025-08-28 13:26:46,262.262] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756358805}' [103 bytes]
[2025-08-28 13:26:46,262.262] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:26:46,262.262] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:26:46,263.263] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:26:46,263.263] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:26:46,263.263] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 286801154}, 'message': '', 'wording': '', 'echo': 1756358805}
[2025-08-28 13:26:46,263.263] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:26:46,263.263] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.403339
[2025-08-28 13:26:56,793.793] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358816,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:26:56,793.793] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756358816, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:27:26,809.809] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358846,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:27:26,810.810] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756358846, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:27:56,820.820] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358876,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:27:56,820.820] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756358876, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:28:26,828.828] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358906,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:28:26,828.828] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756358906, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:28:56,845.845] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358936,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:28:56,845.845] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756358936, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:29:26,859.859] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358966,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:29:26,859.859] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756358966, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:29:33,839.839] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":1420937534,"tim...roup_name":"芜湖兼职群5元1单"}' [512 bytes]
[2025-08-28 13:29:33,840.840] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '[CQ:at,qq=2397778636]', 'message_id': '1849437632', 'message_seq': '1849437632', 'real_id': '1849437632', 'message': "[{'type': 'at', 'data': {'qq': '2397778636'}}, {'type': 'text', 'data': {'text': ' '}}]", 'message_format': 'array'}
[2025-08-28 13:29:33,840.840] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '[CQ:at,qq=2397778636]', 'message_id': '1849437632', 'message_seq': '1849437632', 'real_id': '1849437632', 'message': "[{'type': 'at', 'data': {'qq': '2397778636'}}, {'type': 'text', 'data': {'text': ' '}}]", 'message_format': 'array'}
[2025-08-28 13:29:33,840.840] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.000000
[2025-08-28 13:29:56,861.861] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756358996,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:29:56,861.861] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756358996, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:30:26,873.873] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359026,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:30:26,874.874] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359026, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:30:56,888.888] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359056,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:30:56,888.888] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359056, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:31:03,030.030] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359063,"self_id":3292105955,"post_t...36,"sub_type":"invite"}' [179 bytes]
[2025-08-28 13:31:03,030.030] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_notice_event:193) | {'time': 1756359063, 'self_id': 3292105955, 'post_type': 'notice', 'group_id': 666732394, 'user_id': 3236117226, 'notice_type': 'group_increase', 'operator_id': 2397778636, 'sub_type': 'invite'}
[2025-08-28 13:31:13,876.876] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":2397778636,"tim...roup_name":"芜湖兼职群5元1单"}' [493 bytes]
[2025-08-28 13:31:13,877.877] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '2397778636', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 2397778636, 'nickname': '接插件定制', 'card': ''}", 'raw_message': '用什么指令？', 'message_id': '155544407', 'message_seq': '155544407', 'real_id': '155544407', 'message': "[{'type': 'text', 'data': {'text': '用什么指令？'}}]", 'message_format': 'array'}
[2025-08-28 13:31:13,877.877] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '2397778636', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 2397778636, 'nickname': '接插件定制', 'card': ''}", 'raw_message': '用什么指令？', 'message_id': '155544407', 'message_seq': '155544407', 'real_id': '155544407', 'message': "[{'type': 'text', 'data': {'text': '用什么指令？'}}]", 'message_format': 'array'}
[2025-08-28 13:31:13,877.877] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.000000
[2025-08-28 13:31:26,892.892] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359086,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:31:26,892.892] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359086, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:31:51,288.288] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":2397778636,"tim...roup_name":"芜湖兼职群5元1单"}' [472 bytes]
[2025-08-28 13:31:51,289.289] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '2397778636', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 2397778636, 'nickname': '接插件定制', 'card': ''}", 'raw_message': '你好', 'message_id': '1141373959', 'message_seq': '1141373959', 'real_id': '1141373959', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:31:51,289.289] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '2397778636', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 2397778636, 'nickname': '接插件定制', 'card': ''}", 'raw_message': '你好', 'message_id': '1141373959', 'message_seq': '1141373959', 'real_id': '1141373959', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:31:51,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:51,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:51,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:51,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:51,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:51,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: wGIRDJK0TBoJiSIYHI+Jjw==
[2025-08-28 13:31:51,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:51,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:51,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:51,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:51,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:51,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:51,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:51,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: Da3Eus6f/KoasQ2/kguMln2ifWo=
[2025-08-28 13:31:51,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:51,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359111}' [239 bytes]
[2025-08-28 13:31:51,636.636] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [491 bytes]
[2025-08-28 13:31:51,638.638] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '684247431', 'message_seq': '684247431', 'real_id': '684247431', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:51,638.638] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '684247431', 'message_seq': '684247431', 'real_id': '684247431', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:51,641.641] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:51,641.641] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:51,641.641] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:51,641.641] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:51,641.641] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:51,641.641] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: MfMmWICBbe5yDKBOgX4D2w==
[2025-08-28 13:31:51,641.641] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:51,641.641] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:51,641.641] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:51,642.642] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:51,642.642] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:51,642.642] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:51,642.642] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:51,642.642] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: RB2anFK3iHjCOY9tynxq8nkBXGQ=
[2025-08-28 13:31:51,642.642] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:51,642.642] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359111}' [238 bytes]
[2025-08-28 13:31:51,712.712] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359111}' [104 bytes]
[2025-08-28 13:31:51,712.712] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:51,712.712] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:51,713.713] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:51,713.713] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:51,713.713] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1699208160}, 'message': '', 'wording': '', 'echo': 1756359111}
[2025-08-28 13:31:51,713.713] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:51,713.713] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.424288
[2025-08-28 13:31:51,881.881] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [491 bytes]
[2025-08-28 13:31:51,882.882] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '645808976', 'message_seq': '645808976', 'real_id': '645808976', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:51,882.882] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '645808976', 'message_seq': '645808976', 'real_id': '645808976', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:51,884.884] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:51,884.884] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:51,884.884] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:51,884.884] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:51,884.884] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:51,884.884] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: HEonH52f/BoBErKtwAcQAw==
[2025-08-28 13:31:51,884.884] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:51,884.884] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:51,884.884] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:51,884.884] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:51,885.885] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:51,885.885] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:51,885.885] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:51,885.885] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: zD8+q6vyT7YzwEgFXd3/0nvW0yM=
[2025-08-28 13:31:51,885.885] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:51,885.885] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359111}' [238 bytes]
[2025-08-28 13:31:51,977.977] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359111}' [103 bytes]
[2025-08-28 13:31:51,977.977] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:51,977.977] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:51,977.977] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:51,978.978] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:51,978.978] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 694996464}, 'message': '', 'wording': '', 'echo': 1756359111}
[2025-08-28 13:31:51,978.978] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:51,978.978] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.340093
[2025-08-28 13:31:52,117.117] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [494 bytes]
[2025-08-28 13:31:52,117.117] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '2040092589', 'message_seq': '2040092589', 'real_id': '2040092589', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:52,118.118] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '2040092589', 'message_seq': '2040092589', 'real_id': '2040092589', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:52,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:52,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:52,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:52,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:52,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:52,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: 7xRe6npsURUiEeTXwI6oCg==
[2025-08-28 13:31:52,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:52,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:52,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:52,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:52,121.121] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:52,121.121] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:52,121.121] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:52,121.121] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: hwhL/55HL7E7Hi+uIhMCXsD5WQ0=
[2025-08-28 13:31:52,121.121] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:52,121.121] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359112}' [239 bytes]
[2025-08-28 13:31:52,242.242] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359111}' [104 bytes]
[2025-08-28 13:31:52,243.243] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:52,243.243] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:52,243.243] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:52,243.243] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:52,244.244] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1648412077}, 'message': '', 'wording': '', 'echo': 1756359111}
[2025-08-28 13:31:52,244.244] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:52,244.244] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.362545
[2025-08-28 13:31:52,395.395] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [494 bytes]
[2025-08-28 13:31:52,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '1483377951', 'message_seq': '1483377951', 'real_id': '1483377951', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:52,396.396] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '1483377951', 'message_seq': '1483377951', 'real_id': '1483377951', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:52,398.398] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:52,398.398] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:52,398.398] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:52,398.398] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:52,398.398] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:52,398.398] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: ewMDpOXXh/Lf7ylEOxE4cw==
[2025-08-28 13:31:52,398.398] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:52,398.398] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:52,398.398] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:52,398.398] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:52,399.399] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:52,399.399] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:52,399.399] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:52,399.399] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: G2O6ohQvCR43RCd9gZpwAmNs5mE=
[2025-08-28 13:31:52,399.399] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:52,399.399] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359112}' [239 bytes]
[2025-08-28 13:31:52,504.504] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359112}' [104 bytes]
[2025-08-28 13:31:52,504.504] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:52,504.504] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:52,505.505] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:52,505.505] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:52,505.505] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1884523111}, 'message': '', 'wording': '', 'echo': 1756359112}
[2025-08-28 13:31:52,505.505] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:52,506.506] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.387840
[2025-08-28 13:31:52,663.663] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [494 bytes]
[2025-08-28 13:31:52,663.663] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '1780715199', 'message_seq': '1780715199', 'real_id': '1780715199', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:52,663.663] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '1780715199', 'message_seq': '1780715199', 'real_id': '1780715199', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:52,666.666] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:52,666.666] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:52,666.666] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:52,666.666] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:52,666.666] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:52,666.666] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: yVV+ATcOIc9ZskjvFGMmvQ==
[2025-08-28 13:31:52,666.666] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:52,666.666] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:52,666.666] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:52,666.666] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:52,667.667] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:52,667.667] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:52,667.667] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:52,667.667] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: H2swfCwmJVyaUx3obZfaMYVqgMc=
[2025-08-28 13:31:52,667.667] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:52,667.667] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359112}' [239 bytes]
[2025-08-28 13:31:52,755.755] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359112}' [103 bytes]
[2025-08-28 13:31:52,755.755] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:52,756.756] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:52,756.756] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:52,756.756] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:52,756.756] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 748950648}, 'message': '', 'wording': '', 'echo': 1756359112}
[2025-08-28 13:31:52,756.756] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:52,757.757] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.360851
[2025-08-28 13:31:52,941.941] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [491 bytes]
[2025-08-28 13:31:52,941.941] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '645153484', 'message_seq': '645153484', 'real_id': '645153484', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:52,941.941] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '645153484', 'message_seq': '645153484', 'real_id': '645153484', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:52,943.943] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:52,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:52,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:52,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:52,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:52,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: Cu6M4WjOgtaTxEArl9cXBA==
[2025-08-28 13:31:52,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:52,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:52,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:52,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:52,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:52,945.945] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:52,945.945] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:52,945.945] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: 4ETGQH0Fj/49RJzbzLBNi+thkQs=
[2025-08-28 13:31:52,945.945] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:52,945.945] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359112}' [238 bytes]
[2025-08-28 13:31:53,113.113] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359112}' [104 bytes]
[2025-08-28 13:31:53,113.113] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:53,113.113] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:53,114.114] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:53,114.114] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:53,114.114] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1582003063}, 'message': '', 'wording': '', 'echo': 1756359112}
[2025-08-28 13:31:53,114.114] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:53,115.115] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.451443
[2025-08-28 13:31:53,174.174] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [494 bytes]
[2025-08-28 13:31:53,175.175] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '1433822243', 'message_seq': '1433822243', 'real_id': '1433822243', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:53,175.175] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '1433822243', 'message_seq': '1433822243', 'real_id': '1433822243', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:53,177.177] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:53,177.177] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:53,177.177] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:53,177.177] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:53,177.177] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:53,177.177] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: ie1+jDLM6adZwK/KsoHnUw==
[2025-08-28 13:31:53,177.177] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:53,177.177] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:53,177.177] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:53,177.177] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:53,178.178] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:53,178.178] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:53,178.178] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:53,178.178] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: x++IfwUJytTHjZkHn1qDY50fjyg=
[2025-08-28 13:31:53,178.178] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:53,178.178] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359113}' [203 bytes]
[2025-08-28 13:31:53,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359112}' [103 bytes]
[2025-08-28 13:31:53,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:53,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:53,291.291] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:53,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:53,292.292] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 493765157}, 'message': '', 'wording': '', 'echo': 1756359112}
[2025-08-28 13:31:53,292.292] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:53,292.292] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.351847
[2025-08-28 13:31:53,347.347] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [491 bytes]
[2025-08-28 13:31:53,347.347] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '285371109', 'message_seq': '285371109', 'real_id': '285371109', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:53,348.348] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '285371109', 'message_seq': '285371109', 'real_id': '285371109', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:53,350.350] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:53,350.350] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:53,350.350] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:53,350.350] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:53,350.350] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:53,350.350] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: ICfwNZbbqQFceEN+WmS+9Q==
[2025-08-28 13:31:53,350.350] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:53,350.350] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:53,350.350] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:53,350.350] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:53,351.351] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:53,351.351] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:53,351.351] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:53,351.351] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: tcRhdHif86K7R1k6G6g13CQgxFw=
[2025-08-28 13:31:53,351.351] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:53,351.351] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359113}' [238 bytes]
[2025-08-28 13:31:53,534.534] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359113}' [104 bytes]
[2025-08-28 13:31:53,535.535] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:53,535.535] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:53,535.535] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:53,535.535] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:53,535.535] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1489329120}, 'message': '', 'wording': '', 'echo': 1756359113}
[2025-08-28 13:31:53,536.536] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:31:53,536.536] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.360896
[2025-08-28 13:31:53,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [488 bytes]
[2025-08-28 13:31:53,657.657] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '91812657', 'message_seq': '91812657', 'real_id': '91812657', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:53,657.657] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '91812657', 'message_seq': '91812657', 'real_id': '91812657', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:53,660.660] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:53,660.660] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:53,660.660] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:53,660.660] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:53,660.660] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:53,660.660] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: jxp13KJeDlMN29a8chFpFQ==
[2025-08-28 13:31:53,661.661] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:53,661.661] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:53,661.661] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:53,661.661] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:53,661.661] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:53,661.661] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:53,661.661] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:53,661.661] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: /XKyf32QCYHa3uSWmNQwy7kOxrE=
[2025-08-28 13:31:53,661.661] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:53,662.662] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359113}' [237 bytes]
[2025-08-28 13:31:53,720.720] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359113}' [104 bytes]
[2025-08-28 13:31:53,720.720] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:53,720.720] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:53,721.721] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:53,721.721] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:53,721.721] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1114387598}, 'message': '', 'wording': '', 'echo': 1756359113}
[2025-08-28 13:31:53,721.721] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:53,721.721] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.373883
[2025-08-28 13:31:53,907.907] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [491 bytes]
[2025-08-28 13:31:53,907.907] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '447381828', 'message_seq': '447381828', 'real_id': '447381828', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:53,907.907] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '447381828', 'message_seq': '447381828', 'real_id': '447381828', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:53,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:53,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:53,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:53,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:53,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:53,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: 687P5L9RFElyLIKFB5RBfg==
[2025-08-28 13:31:53,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:53,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:53,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:53,910.910] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:53,911.911] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:53,911.911] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:53,911.911] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:53,911.911] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: R9IZKoykClKtrk2s00iLxrBQMxs=
[2025-08-28 13:31:53,911.911] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:53,911.911] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359113}' [202 bytes]
[2025-08-28 13:31:54,010.010] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359113}' [103 bytes]
[2025-08-28 13:31:54,010.010] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:54,010.010] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:54,011.011] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:54,011.011] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:54,011.011] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 831632637}, 'message': '', 'wording': '', 'echo': 1756359113}
[2025-08-28 13:31:54,011.011] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:54,011.011] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.354010
[2025-08-28 13:31:54,081.081] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [488 bytes]
[2025-08-28 13:31:54,082.082] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '53274516', 'message_seq': '53274516', 'real_id': '53274516', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:54,082.082] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '53274516', 'message_seq': '53274516', 'real_id': '53274516', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:54,084.084] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:54,084.084] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:54,084.084] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:54,084.084] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:54,084.084] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:54,084.084] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: TFaL5S6KMB9k3l96JQAyvg==
[2025-08-28 13:31:54,084.084] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:54,084.084] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:54,084.084] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:54,084.084] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:54,085.085] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:54,085.085] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:54,085.085] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:54,085.085] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: ZAML4gYTKqE/TVCHI6lTOHelb20=
[2025-08-28 13:31:54,085.085] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:54,085.085] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359114}' [201 bytes]
[2025-08-28 13:31:54,250.250] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359113}' [103 bytes]
[2025-08-28 13:31:54,250.250] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:54,250.250] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:54,250.250] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:54,251.251] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:54,251.251] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 881935136}, 'message': '', 'wording': '', 'echo': 1756359113}
[2025-08-28 13:31:54,251.251] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:31:54,251.251] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.343908
[2025-08-28 13:31:54,393.393] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [491 bytes]
[2025-08-28 13:31:54,393.393] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '109934897', 'message_seq': '109934897', 'real_id': '109934897', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:54,393.393] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '109934897', 'message_seq': '109934897', 'real_id': '109934897', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:54,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:54,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:54,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:54,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:54,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:54,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: HYwyKKFestiBTLXeilWa8g==
[2025-08-28 13:31:54,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:54,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:54,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:54,396.396] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:54,397.397] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:54,397.397] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:54,397.397] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:54,397.397] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: UaZmbG5wrux7jNogXlEkBG/8Gsc=
[2025-08-28 13:31:54,397.397] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:54,397.397] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359114}' [202 bytes]
[2025-08-28 13:31:54,484.484] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359114}' [104 bytes]
[2025-08-28 13:31:54,485.485] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:54,485.485] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:54,485.485] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:54,486.486] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:54,486.486] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1925539560}, 'message': '', 'wording': '', 'echo': 1756359114}
[2025-08-28 13:31:54,486.486] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:31:54,486.486] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.404084
[2025-08-28 13:31:54,653.653] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [494 bytes]
[2025-08-28 13:31:54,653.653] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '1095249351', 'message_seq': '1095249351', 'real_id': '1095249351', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:54,653.653] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '1095249351', 'message_seq': '1095249351', 'real_id': '1095249351', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:54,655.655] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:54,655.655] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:54,655.655] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:54,655.655] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: iypBioOlNLW4RWo8Pflmew==
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: 6lrUVLBLrOvZGqDKz5g1HC6zyU4=
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:54,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359114}' [239 bytes]
[2025-08-28 13:31:54,745.745] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359114}' [104 bytes]
[2025-08-28 13:31:54,746.746] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:54,746.746] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:54,746.746] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:54,746.746] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:54,746.746] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1561934091}, 'message': '', 'wording': '', 'echo': 1756359114}
[2025-08-28 13:31:54,746.746] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:31:54,747.747] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.353254
[2025-08-28 13:31:54,936.936] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [494 bytes]
[2025-08-28 13:31:54,937.937] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '2107767925', 'message_seq': '2107767925', 'real_id': '2107767925', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:54,937.937] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '2107767925', 'message_seq': '2107767925', 'real_id': '2107767925', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:54,939.939] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:54,939.939] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:54,940.940] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:54,940.940] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:54,940.940] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:54,940.940] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: xaRBd3kFe5NQhgyVbFxpaA==
[2025-08-28 13:31:54,940.940] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:54,940.940] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:54,940.940] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:54,940.940] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:54,942.942] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:54,942.942] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:54,942.942] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:54,942.942] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: xyikydWRyBo6T2cQTXlT9xA/Iik=
[2025-08-28 13:31:54,942.942] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:54,942.942] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359114}' [239 bytes]
[2025-08-28 13:31:55,047.047] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359114}' [103 bytes]
[2025-08-28 13:31:55,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:55,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:55,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:55,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:55,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 178571453}, 'message': '', 'wording': '', 'echo': 1756359114}
[2025-08-28 13:31:55,048.048] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:55,049.049] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.395699
[2025-08-28 13:31:55,230.230] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [491 bytes]
[2025-08-28 13:31:55,230.230] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '892549711', 'message_seq': '892549711', 'real_id': '892549711', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:55,230.230] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '892549711', 'message_seq': '892549711', 'real_id': '892549711', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:55,233.233] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:55,233.233] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:55,233.233] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:55,233.233] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:55,234.234] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:55,234.234] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: Fah25wXXK510djCFzRagZQ==
[2025-08-28 13:31:55,234.234] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:55,234.234] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:55,234.234] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:55,234.234] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:55,235.235] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:55,235.235] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:55,235.235] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:55,235.235] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: AFI5o6V2UjsHYc4qTX4OeiZT4PM=
[2025-08-28 13:31:55,235.235] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:55,235.235] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359115}' [202 bytes]
[2025-08-28 13:31:55,316.316] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359114}' [104 bytes]
[2025-08-28 13:31:55,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:55,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:55,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:55,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:55,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1386791888}, 'message': '', 'wording': '', 'echo': 1756359114}
[2025-08-28 13:31:55,318.318] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:55,318.318] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.380911
[2025-08-28 13:31:55,475.475] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [494 bytes]
[2025-08-28 13:31:55,476.476] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '2097563725', 'message_seq': '2097563725', 'real_id': '2097563725', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:55,476.476] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '2097563725', 'message_seq': '2097563725', 'real_id': '2097563725', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:55,478.478] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:55,478.478] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:55,478.478] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:55,478.478] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:55,478.478] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:55,478.478] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: uxv5LEYIa7y2rIoER95H1Q==
[2025-08-28 13:31:55,478.478] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:55,479.479] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:55,479.479] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:55,479.479] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:55,479.479] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:55,479.479] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:55,479.479] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:55,479.479] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: xh3zwoPwYCJJbggJckF3oKNDJnA=
[2025-08-28 13:31:55,479.479] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:55,479.479] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359115}' [239 bytes]
[2025-08-28 13:31:55,629.629] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359115}' [103 bytes]
[2025-08-28 13:31:55,629.629] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:55,629.629] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:55,631.631] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:55,631.631] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:55,632.632] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 178098466}, 'message': '', 'wording': '', 'echo': 1756359115}
[2025-08-28 13:31:55,632.632] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:31:55,632.632] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.401087
[2025-08-28 13:31:55,808.808] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [491 bytes]
[2025-08-28 13:31:55,808.808] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '340854130', 'message_seq': '340854130', 'real_id': '340854130', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:55,809.809] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好呀', 'message_id': '340854130', 'message_seq': '340854130', 'real_id': '340854130', 'message': "[{'type': 'text', 'data': {'text': '你好呀'}}]", 'message_format': 'array'}
[2025-08-28 13:31:55,811.811] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:31:55,811.811] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:31:55,811.811] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:31:55,811.811] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:31:55,811.811] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:31:55,811.811] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: EtgRU1sJ4iXkDqHseDYkSQ==
[2025-08-28 13:31:55,811.811] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:31:55,811.811] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:31:55,811.811] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:31:55,811.811] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:31:55,812.812] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:31:55,812.812] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:31:55,812.812] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:31:55,812.812] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: 6CQ/iGNI1WekOjTaii2b7UU3PnE=
[2025-08-28 13:31:55,812.812] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:31:55,812.812] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359115}' [202 bytes]
[2025-08-28 13:31:55,874.874] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359115}' [103 bytes]
[2025-08-28 13:31:55,874.874] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:55,874.874] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:55,875.875] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:55,875.875] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:55,875.875] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 623030401}, 'message': '', 'wording': '', 'echo': 1756359115}
[2025-08-28 13:31:55,875.875] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:31:55,875.875] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.399086
[2025-08-28 13:31:56,223.223] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359115}' [104 bytes]
[2025-08-28 13:31:56,223.223] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:31:56,223.223] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:56,223.223] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:31:56,223.223] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:31:56,224.224] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1803813127}, 'message': '', 'wording': '', 'echo': 1756359115}
[2025-08-28 13:31:56,224.224] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:31:56,224.224] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.416366
[2025-08-28 13:31:56,894.894] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359116,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:31:56,894.894] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359116, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:32:24,365.365] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":2397778636,"tim...roup_name":"芜湖兼职群5元1单"}' [472 bytes]
[2025-08-28 13:32:24,366.366] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '2397778636', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 2397778636, 'nickname': '接插件定制', 'card': ''}", 'raw_message': '你好', 'message_id': '1970945141', 'message_seq': '1970945141', 'real_id': '1970945141', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:32:24,366.366] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '2397778636', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 2397778636, 'nickname': '接插件定制', 'card': ''}", 'raw_message': '你好', 'message_id': '1970945141', 'message_seq': '1970945141', 'real_id': '1970945141', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:32:24,368.368] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:24,368.368] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:24,368.368] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:24,368.368] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:24,368.368] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:24,368.368] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: w5sbkq/Yvx7PlfGzLqtMgQ==
[2025-08-28 13:32:24,368.368] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:24,369.369] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:24,369.369] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:24,369.369] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:24,369.369] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:24,369.369] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:24,369.369] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:24,369.369] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: ogCBMehapFRqkPzFFYDZgFLvqh0=
[2025-08-28 13:32:24,369.369] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:24,369.369] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359144}' [203 bytes]
[2025-08-28 13:32:24,545.545] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [492 bytes]
[2025-08-28 13:32:24,545.545] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1373180631', 'message_seq': '1373180631', 'real_id': '1373180631', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:24,545.545] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1373180631', 'message_seq': '1373180631', 'real_id': '1373180631', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:24,547.547] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:24,547.547] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:24,547.547] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:24,547.547] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:24,547.547] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: AA3dSl10cgB6Rz3R7XU2gg==
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: l4hHyvmSjWGun0rUL/2keX8yMx4=
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:24,548.548] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359144}' [239 bytes]
[2025-08-28 13:32:24,769.769] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359144}' [104 bytes]
[2025-08-28 13:32:24,769.769] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:24,769.769] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:24,769.769] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:24,769.769] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:24,769.769] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1427629038}, 'message': '', 'wording': '', 'echo': 1756359144}
[2025-08-28 13:32:24,769.769] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:24,769.769] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.403699
[2025-08-28 13:32:24,819.819] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [489 bytes]
[2025-08-28 13:32:24,819.819] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '525998714', 'message_seq': '525998714', 'real_id': '525998714', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:24,819.819] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '525998714', 'message_seq': '525998714', 'real_id': '525998714', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:24,821.821] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: K1g6vj5oXwnlOGBQTBOH/w==
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:24,822.822] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:24,823.823] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: 5xC4Rxk1xx2T3GoFaLSyLEdlF2A=
[2025-08-28 13:32:24,823.823] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:24,823.823] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359144}' [202 bytes]
[2025-08-28 13:32:24,974.974] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359144}' [104 bytes]
[2025-08-28 13:32:24,975.975] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:24,975.975] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:24,975.975] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:24,976.976] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:24,976.976] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1567977382}, 'message': '', 'wording': '', 'echo': 1756359144}
[2025-08-28 13:32:24,976.976] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:32:24,976.976] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.430972
[2025-08-28 13:32:25,064.064] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [489 bytes]
[2025-08-28 13:32:25,064.064] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '410216054', 'message_seq': '410216054', 'real_id': '410216054', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:25,064.064] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '410216054', 'message_seq': '410216054', 'real_id': '410216054', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:25,067.067] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:25,067.067] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:25,067.067] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:25,067.067] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: A7CzomZXiBCdXCOpXmRzng==
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: L5dWlvsc2UkaEqUmpWjpXI4dn6Q=
[2025-08-28 13:32:25,068.068] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:25,069.069] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359145}' [202 bytes]
[2025-08-28 13:32:25,213.213] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359144}' [104 bytes]
[2025-08-28 13:32:25,214.214] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:25,214.214] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:25,215.215] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:25,215.215] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:25,215.215] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1534863585}, 'message': '', 'wording': '', 'echo': 1756359144}
[2025-08-28 13:32:25,215.215] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:25,216.216] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.396122
[2025-08-28 13:32:25,268.268] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [492 bytes]
[2025-08-28 13:32:25,268.268] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1765494758', 'message_seq': '1765494758', 'real_id': '1765494758', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:25,269.269] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1765494758', 'message_seq': '1765494758', 'real_id': '1765494758', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:25,271.271] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:25,271.271] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:25,271.271] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:25,271.271] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:25,271.271] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:25,271.271] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: j68g10gpzxZSncfODN/05g==
[2025-08-28 13:32:25,271.271] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:25,271.271] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:25,271.271] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:25,272.272] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:25,272.272] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:25,272.272] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:25,272.272] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:25,272.272] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: zdyZZbD/ubB7ckqXVUX74gGcTOw=
[2025-08-28 13:32:25,272.272] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:25,272.272] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359145}' [203 bytes]
[2025-08-28 13:32:25,552.552] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359145}' [104 bytes]
[2025-08-28 13:32:25,552.552] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:25,552.552] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:25,552.552] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:25,552.552] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:25,553.553] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1978474111}, 'message': '', 'wording': '', 'echo': 1756359145}
[2025-08-28 13:32:25,553.553] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:25,553.553] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.488244
[2025-08-28 13:32:25,643.643] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [489 bytes]
[2025-08-28 13:32:25,643.643] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '926944378', 'message_seq': '926944378', 'real_id': '926944378', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:25,643.643] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '926944378', 'message_seq': '926944378', 'real_id': '926944378', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:25,646.646] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:25,646.646] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:25,646.646] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:25,646.646] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:25,646.646] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:25,646.646] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: +E+7SXjH7urKQ/TYIzEX9w==
[2025-08-28 13:32:25,647.647] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:25,647.647] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:25,647.647] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:25,647.647] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:25,647.647] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:25,647.647] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:25,647.647] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:25,647.647] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: fGgpCLG/XmZnoZAiy3sYh6Wc4ys=
[2025-08-28 13:32:25,647.647] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:25,647.647] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359145}' [238 bytes]
[2025-08-28 13:32:25,691.691] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359145}' [104 bytes]
[2025-08-28 13:32:25,692.692] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:25,692.692] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:25,692.692] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:25,692.692] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:25,692.692] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1086744569}, 'message': '', 'wording': '', 'echo': 1756359145}
[2025-08-28 13:32:25,692.692] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:25,692.692] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.423484
[2025-08-28 13:32:25,828.828] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [489 bytes]
[2025-08-28 13:32:25,828.828] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '383281106', 'message_seq': '383281106', 'real_id': '383281106', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:25,828.828] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '383281106', 'message_seq': '383281106', 'real_id': '383281106', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:25,830.830] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:25,830.830] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:25,830.830] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:25,830.830] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:25,831.831] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:25,831.831] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: nJ12e1Dqe1mexseOsTlZJg==
[2025-08-28 13:32:25,831.831] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:25,831.831] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:25,831.831] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:25,831.831] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:25,832.832] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:25,832.832] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:25,832.832] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:25,832.832] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: 58eTX9kqGc9NhRKXZcdkEu/Vo20=
[2025-08-28 13:32:25,832.832] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:25,832.832] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359145}' [202 bytes]
[2025-08-28 13:32:25,998.998] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [492 bytes]
[2025-08-28 13:32:25,998.998] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1559770026', 'message_seq': '1559770026', 'real_id': '1559770026', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:25,998.998] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1559770026', 'message_seq': '1559770026', 'real_id': '1559770026', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:26,000.000] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: d7LmMQwx+S6SBDPnEcK91A==
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:26,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: PXPpJAJ13Ye5jTr2ts+LwtnTh2g=
[2025-08-28 13:32:26,002.002] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:26,002.002] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359146}' [203 bytes]
[2025-08-28 13:32:26,046.046] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359145}' [104 bytes]
[2025-08-28 13:32:26,046.046] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:26,046.046] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:26,047.047] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:26,047.047] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:26,047.047] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1858994417}, 'message': '', 'wording': '', 'echo': 1756359145}
[2025-08-28 13:32:26,047.047] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:32:26,047.047] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.403948
[2025-08-28 13:32:26,169.169] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [489 bytes]
[2025-08-28 13:32:26,169.169] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '972585669', 'message_seq': '972585669', 'real_id': '972585669', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:26,169.169] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '972585669', 'message_seq': '972585669', 'real_id': '972585669', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:26,171.171] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:26,171.171] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:26,171.171] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: RNRLaT9Pcis1UCpDXo4Gew==
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: PJIFMEvAG82DX9lGASg0H8fCMhQ=
[2025-08-28 13:32:26,172.172] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:26,173.173] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359146}' [202 bytes]
[2025-08-28 13:32:26,232.232] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359145}' [104 bytes]
[2025-08-28 13:32:26,232.232] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:26,232.232] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:26,233.233] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:26,233.233] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:26,233.233] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1974132070}, 'message': '', 'wording': '', 'echo': 1756359145}
[2025-08-28 13:32:26,233.233] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:26,233.233] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.405234
[2025-08-28 13:32:26,378.378] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359146}' [104 bytes]
[2025-08-28 13:32:26,378.378] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:26,378.378] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:26,378.378] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:26,379.379] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:26,379.379] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1605674609}, 'message': '', 'wording': '', 'echo': 1756359146}
[2025-08-28 13:32:26,379.379] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:26,379.379] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.380618
[2025-08-28 13:32:26,455.455] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [492 bytes]
[2025-08-28 13:32:26,456.456] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1870999278', 'message_seq': '1870999278', 'real_id': '1870999278', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:26,456.456] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1870999278', 'message_seq': '1870999278', 'real_id': '1870999278', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:26,458.458] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:26,458.458] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:26,458.458] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:26,458.458] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:26,458.458] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:26,458.458] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: /GmIAUaK3U+GRJRhnr5x0A==
[2025-08-28 13:32:26,458.458] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:26,459.459] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:26,459.459] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:26,459.459] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:26,459.459] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:26,459.459] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:26,459.459] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:26,459.459] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: pWDWoq01BV0WUfhfMiefCzgUBgE=
[2025-08-28 13:32:26,459.459] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:26,459.459] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359146}' [203 bytes]
[2025-08-28 13:32:26,559.559] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359146}' [103 bytes]
[2025-08-28 13:32:26,559.559] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:26,559.559] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:26,560.560] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:26,560.560] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:26,560.560] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 807544256}, 'message': '', 'wording': '', 'echo': 1756359146}
[2025-08-28 13:32:26,560.560] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:26,560.560] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.390742
[2025-08-28 13:32:26,682.682] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [492 bytes]
[2025-08-28 13:32:26,684.684] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1691701233', 'message_seq': '1691701233', 'real_id': '1691701233', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:26,684.684] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1691701233', 'message_seq': '1691701233', 'real_id': '1691701233', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:26,686.686] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: 3W+vTW4daTlohgVnaKG2pA==
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:26,687.687] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:26,688.688] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:26,688.688] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: aIrDzAnCzwd5Fe0PM9lgc60HJmM=
[2025-08-28 13:32:26,688.688] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:26,688.688] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359146}' [203 bytes]
[2025-08-28 13:32:26,882.882] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359146}' [104 bytes]
[2025-08-28 13:32:26,882.882] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:26,882.882] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:26,882.882] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:26,882.882] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:26,883.883] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1459009379}, 'message': '', 'wording': '', 'echo': 1756359146}
[2025-08-28 13:32:26,883.883] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:26,883.883] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.426624
[2025-08-28 13:32:26,904.904] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359146,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:32:26,905.905] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359146, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:32:26,953.953] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [492 bytes]
[2025-08-28 13:32:26,954.954] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1816301728', 'message_seq': '1816301728', 'real_id': '1816301728', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:26,954.954] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1816301728', 'message_seq': '1816301728', 'real_id': '1816301728', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:26,956.956] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:26,956.956] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:26,956.956] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: bFzPDw/2wNAdSlxhPm9+sA==
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: +XzZHXO2WQLmWl9Hr3qH0zqHYGs=
[2025-08-28 13:32:26,957.957] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:26,958.958] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359146}' [203 bytes]
[2025-08-28 13:32:27,116.116] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [489 bytes]
[2025-08-28 13:32:27,116.116] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '794042190', 'message_seq': '794042190', 'real_id': '794042190', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:27,116.116] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '794042190', 'message_seq': '794042190', 'real_id': '794042190', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:27,118.118] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:27,118.118] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: +q+zOjZHdT7fUAXmbyo12g==
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:27,119.119] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:27,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: Zu/SkpVbY8xEYfKkJLkGIHBiV0Y=
[2025-08-28 13:32:27,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:27,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359147}' [238 bytes]
[2025-08-28 13:32:27,230.230] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359146}' [104 bytes]
[2025-08-28 13:32:27,230.230] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:27,230.230] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:27,231.231] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:27,231.231] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:27,232.232] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1139786293}, 'message': '', 'wording': '', 'echo': 1756359146}
[2025-08-28 13:32:27,232.232] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:27,232.232] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.548749
[2025-08-28 13:32:27,348.348] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359146}' [104 bytes]
[2025-08-28 13:32:27,348.348] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:27,349.349] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:27,349.349] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:27,349.349] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:27,349.349] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1863351839}, 'message': '', 'wording': '', 'echo': 1756359146}
[2025-08-28 13:32:27,349.349] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:27,350.350] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.395415
[2025-08-28 13:32:27,509.509] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [489 bytes]
[2025-08-28 13:32:27,510.510] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '513120908', 'message_seq': '513120908', 'real_id': '513120908', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:27,510.510] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '513120908', 'message_seq': '513120908', 'real_id': '513120908', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:27,512.512] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:27,512.512] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:27,512.512] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:27,512.512] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:27,512.512] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:27,512.512] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: pLfMDSQY9ULJ1Ob5TdirMg==
[2025-08-28 13:32:27,512.512] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:27,513.513] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:27,513.513] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:27,513.513] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:27,513.513] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:27,513.513] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:27,513.513] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:27,513.513] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: LJvFiUYp6cy+4wr4UTSnkvFy/mQ=
[2025-08-28 13:32:27,513.513] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:27,513.513] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359147}' [202 bytes]
[2025-08-28 13:32:27,589.589] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359147}' [104 bytes]
[2025-08-28 13:32:27,589.589] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:27,589.589] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:27,590.590] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:27,590.590] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:27,590.590] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1867165467}, 'message': '', 'wording': '', 'echo': 1756359147}
[2025-08-28 13:32:27,590.590] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 我是你的专属机器人
[2025-08-28 13:32:27,591.591] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.474505
[2025-08-28 13:32:27,653.653] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [486 bytes]
[2025-08-28 13:32:27,653.653] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '56684612', 'message_seq': '56684612', 'real_id': '56684612', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:27,653.653] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '56684612', 'message_seq': '56684612', 'real_id': '56684612', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:27,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:27,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:27,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:27,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:27,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:27,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: /VPCJdH/oUr4w0cOj2NESQ==
[2025-08-28 13:32:27,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:27,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:27,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:27,656.656] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:27,657.657] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:27,657.657] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:27,657.657] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:27,657.657] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: H+lZkMpFe1I1gySTMmprNK9LFfo=
[2025-08-28 13:32:27,657.657] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:27,657.657] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359147}' [201 bytes]
[2025-08-28 13:32:27,804.804] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [489 bytes]
[2025-08-28 13:32:27,804.804] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '776278786', 'message_seq': '776278786', 'real_id': '776278786', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:27,804.804] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '776278786', 'message_seq': '776278786', 'real_id': '776278786', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:27,806.806] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:27,806.806] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:27,806.806] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:27,806.806] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:27,806.806] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:27,807.807] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: IrXlFxIOklQI6OMg2MCyBw==
[2025-08-28 13:32:27,807.807] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:27,807.807] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:27,807.807] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:27,807.807] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:27,807.807] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:27,807.807] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:27,807.807] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:27,807.807] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: dqyQscJTG5oyvMyUmsuZLW1TVTE=
[2025-08-28 13:32:27,807.807] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:27,808.808] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359147}' [202 bytes]
[2025-08-28 13:32:27,888.888] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359147}' [103 bytes]
[2025-08-28 13:32:27,888.888] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:27,888.888] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:27,888.888] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:27,889.889] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:27,889.889] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 459215126}, 'message': '', 'wording': '', 'echo': 1756359147}
[2025-08-28 13:32:27,889.889] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:27,890.890] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.379739
[2025-08-28 13:32:27,994.994] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [492 bytes]
[2025-08-28 13:32:27,995.995] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1604075793', 'message_seq': '1604075793', 'real_id': '1604075793', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:27,995.995] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '1604075793', 'message_seq': '1604075793', 'real_id': '1604075793', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:27,998.998] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:27,998.998] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:28,000.000] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:28,000.000] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:28,000.000] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:28,000.000] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: dQ8+cBXplVqMrq7WnTdn5g==
[2025-08-28 13:32:28,000.000] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:28,000.000] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:28,000.000] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:28,000.000] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:28,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:28,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:28,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:28,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: 6XKM9I97OG9h24jKl2t3jP/sRwM=
[2025-08-28 13:32:28,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:28,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359148}' [203 bytes]
[2025-08-28 13:32:28,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359147}' [104 bytes]
[2025-08-28 13:32:28,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:28,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:28,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:28,049.049] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:28,049.049] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1273226547}, 'message': '', 'wording': '', 'echo': 1756359147}
[2025-08-28 13:32:28,049.049] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:28,049.049] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.395402
[2025-08-28 13:32:28,192.192] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [492 bytes]
[2025-08-28 13:32:28,192.192] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '2007933099', 'message_seq': '2007933099', 'real_id': '2007933099', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:28,193.193] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '你好11', 'message_id': '2007933099', 'message_seq': '2007933099', 'real_id': '2007933099', 'message': "[{'type': 'text', 'data': {'text': '你好11'}}]", 'message_format': 'array'}
[2025-08-28 13:32:28,195.195] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:28,195.195] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:28,195.195] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:28,195.195] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:28,195.195] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:28,195.195] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: AVXL5yKBzpmF+wDJW3FyuA==
[2025-08-28 13:32:28,195.195] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:28,195.195] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:28,196.196] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:28,196.196] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:28,207.207] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359147}' [104 bytes]
[2025-08-28 13:32:28,207.207] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:28,208.208] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:28,210.210] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:28,211.211] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:28,211.211] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:28,211.211] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:28,211.211] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: qXlZKcBUwAk7pOm2XPp6vEqIVYU=
[2025-08-28 13:32:28,211.211] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:28,212.212] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359148}' [203 bytes]
[2025-08-28 13:32:28,212.212] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:28,212.212] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1350922616}, 'message': '', 'wording': '', 'echo': 1756359147}
[2025-08-28 13:32:28,212.212] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:28,213.213] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.408417
[2025-08-28 13:32:28,372.372] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359148}' [103 bytes]
[2025-08-28 13:32:28,372.372] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:28,372.372] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:28,372.372] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:28,372.372] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:28,373.373] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 475908527}, 'message': '', 'wording': '', 'echo': 1756359148}
[2025-08-28 13:32:28,373.373] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:28,373.373] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.377964
[2025-08-28 13:32:28,624.624] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359148}' [104 bytes]
[2025-08-28 13:32:28,624.624] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:28,624.624] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:28,625.625] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:28,625.625] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:28,625.625] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1715139995}, 'message': '', 'wording': '', 'echo': 1756359148}
[2025-08-28 13:32:28,625.625] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:28,625.625] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.432532
[2025-08-28 13:32:38,585.585] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":2397778636,"tim...roup_name":"芜湖兼职群5元1单"}' [475 bytes]
[2025-08-28 13:32:38,585.585] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '2397778636', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 2397778636, 'nickname': '接插件定制', 'card': ''}", 'raw_message': '你好333', 'message_id': '276192046', 'message_seq': '276192046', 'real_id': '276192046', 'message': "[{'type': 'text', 'data': {'text': '你好333'}}]", 'message_format': 'array'}
[2025-08-28 13:32:38,585.585] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '2397778636', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 2397778636, 'nickname': '接插件定制', 'card': ''}", 'raw_message': '你好333', 'message_id': '276192046', 'message_seq': '276192046', 'real_id': '276192046', 'message': "[{'type': 'text', 'data': {'text': '你好333'}}]", 'message_format': 'array'}
[2025-08-28 13:32:38,587.587] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:32:38,587.587] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:32:38,587.587] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:32:38,587.587] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:32:38,587.587] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:32:38,587.587] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: JndTA8drIEMAM//yfKYw7Q==
[2025-08-28 13:32:38,588.588] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:32:38,588.588] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:32:38,588.588] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:32:38,588.588] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:32:38,588.588] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:32:38,589.589] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:32:38,589.589] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:32:38,589.589] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: cU3aCd+Wpn7pSqJiQ0/ZDgC7bHc=
[2025-08-28 13:32:38,589.589] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:32:38,589.589] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359158}' [202 bytes]
[2025-08-28 13:32:38,942.942] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359158}' [103 bytes]
[2025-08-28 13:32:38,942.942] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:32:38,942.942] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:38,943.943] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:32:38,943.943] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:32:38,943.943] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 221066743}, 'message': '', 'wording': '', 'echo': 1756359158}
[2025-08-28 13:32:38,943.943] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 你也好
[2025-08-28 13:32:38,943.943] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.358225
[2025-08-28 13:33:58,765.765] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 13:33:58,766.766] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 13:33:58,767.767] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 13:33:58,768.768] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 13:33:58,768.768] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 13:33:58,771.771] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 13:33:58,772.772] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 13:33:58,772.772] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 13:33:58,772.772] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 13:33:58,772.772] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 13:33:58,893.893] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 13:33:58,960.960] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:33:59,146.146] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 13:33:59,514.514] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:33:59,601.601] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:33:59,758.758] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:34:00,303.303] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 13:34:00,478.478] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:34:02,135.135] WARNING  [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:173) | 建议设置好 root 账号保证权限功能能够正常使用
[2025-08-28 13:34:02,135.135] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:174) | [BOTQQ]: 3292105955 | [WSURI]: ws://localhost:3001 | [WS_TOKEN]:  | [ROOT]: 123456 | [WEBUI]: http://localhost:6099
[2025-08-28 13:34:02,135.135] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (config.py:validate_config:186) | 未启用插件白名单或黑名单，将加载所有插件
[2025-08-28 13:34:02,135.135] DEBUG    [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:172) | 加载权限
[2025-08-28 13:34:02,136.136] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:178) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 13:34:02,136.136] WARNING  [Thread-2 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:183) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 13:34:02,136.136] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 13:34:02,140.140] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:34:02,140.140] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET / HTTP/1.1
[2025-08-28 13:34:02,140.140] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:34:02,140.140] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:34:02,140.140] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:34:02,140.140] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: tzfyFdgqmAqb2bgUGIXr7g==
[2025-08-28 13:34:02,141.141] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:34:02,141.141] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:34:02,141.141] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:34:02,141.141] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:34:02,141.141] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:34:02,141.141] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:34:02,142.142] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:34:02,142.142] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: pXPHJDVQej6NCe6vVPG8IyW7nS8=
[2025-08-28 13:34:02,142.142] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:34:02,142.142] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (wsroute.py:check_websocket:29) | WebSocket ws://localhost:3001 可用.
[2025-08-28 13:34:02,142.142] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:34:02,142.142] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:34:02,142.142] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359242,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 13:34:02,142.142] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:34:02,142.142] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:34:02,144.144] INFO     [Thread-2 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:ncatbot_service_remote_start:51) | napcat 服务器 ws://localhost:3001 在线, 连接中...
[2025-08-28 13:34:02,165.165] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 13:34:04,220.220] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/auth/login HTTP/1.1" 200 315
[2025-08-28 13:34:04,241.241] DEBUG    [Thread-2 (<lambda>)|MainProcess] adapter.nc.login (login.py:__init__:63) | 成功连接到 WEBUI
[2025-08-28 13:34:04,242.242] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 13:34:06,313.313] DEBUG    [Thread-2 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/QQLogin/GetQQLoginInfo HTTP/1.1" 200 124
[2025-08-28 13:34:06,313.313] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (client.py:run:319) | NapCat 服务启动登录完成
[2025-08-28 13:34:06,313.313] DEBUG    [Thread-2 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 13:34:06,314.314] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:info_subscribe_message_types:250) | 已订阅消息类型:[群聊]全部消息类型
[2025-08-28 13:34:06,314.314] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:info_subscribe_message_types:251) | 已订阅消息类型:[私聊]全部消息类型
[2025-08-28 13:34:06,314.314] INFO     [Thread-2 (<lambda>)|MainProcess] PluginLoader (loader.py:load_plugins:474) | 插件目录: F:\soft\python\qq\napcat\NapCat.34740.Shell\plugins 不存在......跳过加载插件
[2025-08-28 13:34:06,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:34:06,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /event HTTP/1.1
[2025-08-28 13:34:06,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:34:06,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:34:06,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:34:06,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: 0gTP4/IGDOLg4sQCRL51Yg==
[2025-08-28 13:34:06,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:34:06,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:34:06,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:34:06,317.317] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:34:06,318.318] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:34:06,318.318] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:34:06,318.318] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:34:06,318.318] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: a6RD4Jt5TqEKck8g0YIDWDtEi7Q=
[2025-08-28 13:34:06,318.318] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:34:06,318.318] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359246,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 13:34:06,318.318] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:43) | 机器人 3292105955 成功启动
[2025-08-28 13:34:08,476.476] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:3000
[2025-08-28 13:34:10,516.516] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:3000 "GET /get_group_list HTTP/1.1" 200 7139
[2025-08-28 13:34:26,277.277] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":1420937534,"tim...roup_name":"芜湖兼职群5元1单"}' [461 bytes]
[2025-08-28 13:34:26,277.277] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'message_id': '1040011919', 'message_seq': '1040011919', 'real_id': '1040011919', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:34:26,277.277] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'message_id': '1040011919', 'message_seq': '1040011919', 'real_id': '1040011919', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:34:26,280.280] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:34:26,280.280] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:34:26,280.280] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: QtMi8jW5dLJKvpWJuJsXZw==
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: BJrgXaxq0BaFlb07TuS4pwh0q0c=
[2025-08-28 13:34:26,281.281] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:34:26,282.282] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756359266}' [186 bytes]
[2025-08-28 13:34:26,571.571] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [482 bytes]
[2025-08-28 13:34:26,572.572] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '111', 'message_id': '1883659823', 'message_seq': '1883659823', 'real_id': '1883659823', 'message': "[{'type': 'text', 'data': {'text': '111'}}]", 'message_format': 'array'}
[2025-08-28 13:34:26,572.572] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '111', 'message_id': '1883659823', 'message_seq': '1883659823', 'real_id': '1883659823', 'message': "[{'type': 'text', 'data': {'text': '111'}}]", 'message_format': 'array'}
[2025-08-28 13:34:26,572.572] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.000000
[2025-08-28 13:34:26,722.722] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756359266}' [104 bytes]
[2025-08-28 13:34:26,722.722] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:34:26,722.722] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:34:26,722.722] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:34:26,723.723] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:34:26,723.723] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 1568272711}, 'message': '', 'wording': '', 'echo': 1756359266}
[2025-08-28 13:34:26,723.723] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:51) | 在群 666732394 中触发关键词 '你好'，回复: 1
[2025-08-28 13:34:26,723.723] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.445990
[2025-08-28 13:34:26,944.944] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359266,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:34:26,945.945] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359266, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:34:56,952.952] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359296,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:34:56,953.953] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359296, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:35:26,961.961] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359326,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:35:26,962.962] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359326, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:35:56,963.963] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359356,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:35:56,964.964] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359356, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:36:26,965.965] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359386,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:36:26,965.965] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359386, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:36:30,120.120] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":1420937534,"tim...roup_name":"芜湖兼职群5元1单"}' [488 bytes]
[2025-08-28 13:36:30,121.121] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '我再优化下速度', 'message_id': '377305651', 'message_seq': '377305651', 'real_id': '377305651', 'message': "[{'type': 'text', 'data': {'text': '我再优化下速度'}}]", 'message_format': 'array'}
[2025-08-28 13:36:30,121.121] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '我再优化下速度', 'message_id': '377305651', 'message_seq': '377305651', 'real_id': '377305651', 'message': "[{'type': 'text', 'data': {'text': '我再优化下速度'}}]", 'message_format': 'array'}
[2025-08-28 13:36:30,121.121] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.000000
[2025-08-28 13:36:56,976.976] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359416,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:36:56,976.976] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359416, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:37:26,980.980] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359446,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:37:26,980.980] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359446, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:37:44,878.878] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":2397778636,"tim...roup_name":"芜湖兼职群5元1单"}' [466 bytes]
[2025-08-28 13:37:44,878.878] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '2397778636', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 2397778636, 'nickname': '接插件定制', 'card': ''}", 'raw_message': '好', 'message_id': '1583921876', 'message_seq': '1583921876', 'real_id': '1583921876', 'message': "[{'type': 'text', 'data': {'text': '好'}}]", 'message_format': 'array'}
[2025-08-28 13:37:44,878.878] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:28) | {'group_id': '666732394', 'user_id': '2397778636', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 2397778636, 'nickname': '接插件定制', 'card': ''}", 'raw_message': '好', 'message_id': '1583921876', 'message_seq': '1583921876', 'real_id': '1583921876', 'message': "[{'type': 'text', 'data': {'text': '好'}}]", 'message_format': 'array'}
[2025-08-28 13:37:44,879.879] INFO     [Thread-2 (<lambda>)|MainProcess] Logger (main.py:on_group_message:54) | 处理时间: 0.000974
[2025-08-28 13:37:56,996.996] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359476,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:37:56,996.996] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359476, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:38:27,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359507,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:38:27,001.001] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359507, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:38:57,006.006] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359537,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:38:57,006.006] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359537, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:39:27,017.017] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359567,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:39:27,017.017] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359567, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:39:57,022.022] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359597,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:39:57,023.023] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359597, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:40:27,023.023] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359627,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:40:27,024.024] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359627, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:40:57,028.028] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359657,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:40:57,028.028] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359657, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:41:27,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359687,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:41:27,048.048] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359687, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:41:57,057.057] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359717,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:41:57,057.057] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359717, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:42:27,059.059] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359747,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:42:27,060.060] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359747, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:42:57,076.076] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359777,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:42:57,077.077] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359777, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:43:27,082.082] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359807,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:43:27,082.082] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359807, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:43:57,085.085] DEBUG    [Thread-2 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359837,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:43:57,085.085] DEBUG    [Thread-2 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359837, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:44:04,003.003] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 13:44:04,004.004] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 13:44:04,005.005] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 13:44:04,006.006] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 13:44:04,007.007] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 13:44:04,007.007] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 13:44:04,007.007] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 13:44:04,009.009] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 13:44:04,009.009] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 13:44:04,009.009] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 13:44:04,188.188] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 13:44:04,257.257] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:44:04,388.388] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 13:44:04,692.692] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 13:44:05,498.498] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:44:05,606.606] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:44:05,687.687] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:44:07,316.316] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:44:37,595.595] DEBUG    [Thread-2 (load_in_thread)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:3000
[2025-08-28 13:44:39,639.639] DEBUG    [Thread-2 (load_in_thread)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:3000 "GET /get_group_list HTTP/1.1" 200 7139
[2025-08-28 13:44:47,101.101] WARNING  [Thread-3 (<lambda>)|MainProcess] Logger (config.py:validate_config:173) | 建议设置好 root 账号保证权限功能能够正常使用
[2025-08-28 13:44:47,101.101] INFO     [Thread-3 (<lambda>)|MainProcess] Logger (config.py:validate_config:174) | [BOTQQ]: 3292105955 | [WSURI]: ws://localhost:3001 | [WS_TOKEN]:  | [ROOT]: 123456 | [WEBUI]: http://localhost:6099
[2025-08-28 13:44:47,102.102] INFO     [Thread-3 (<lambda>)|MainProcess] Logger (config.py:validate_config:186) | 未启用插件白名单或黑名单，将加载所有插件
[2025-08-28 13:44:47,102.102] DEBUG    [Thread-3 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:172) | 加载权限
[2025-08-28 13:44:47,102.102] WARNING  [Thread-3 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:178) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 13:44:47,102.102] WARNING  [Thread-3 (<lambda>)|MainProcess] AccessController (access_controller.py:_load_access:183) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 13:44:47,103.103] DEBUG    [Thread-3 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 13:44:47,106.106] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:44:47,106.106] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET / HTTP/1.1
[2025-08-28 13:44:47,106.106] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:44:47,106.106] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:44:47,107.107] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:44:47,107.107] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: LGtMf8sKcPse7Tx/ArvYKQ==
[2025-08-28 13:44:47,107.107] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:44:47,107.107] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:44:47,107.107] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:44:47,107.107] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:44:47,110.110] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:44:47,110.110] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:44:47,111.111] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:44:47,111.111] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: 2z7rBVn/i8QEzdct+yrGjZEzpaM=
[2025-08-28 13:44:47,111.111] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:44:47,111.111] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (wsroute.py:check_websocket:29) | WebSocket ws://localhost:3001 可用.
[2025-08-28 13:44:47,111.111] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:44:47,111.111] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:44:47,112.112] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359887,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 13:44:47,114.114] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:44:47,114.114] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:44:47,116.116] INFO     [Thread-3 (<lambda>)|MainProcess] adapter.nc.launcher (launcher.py:ncatbot_service_remote_start:51) | napcat 服务器 ws://localhost:3001 在线, 连接中...
[2025-08-28 13:44:47,138.138] DEBUG    [Thread-3 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 13:44:49,185.185] DEBUG    [Thread-3 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/auth/login HTTP/1.1" 200 315
[2025-08-28 13:44:49,206.206] DEBUG    [Thread-3 (<lambda>)|MainProcess] adapter.nc.login (login.py:__init__:63) | 成功连接到 WEBUI
[2025-08-28 13:44:49,208.208] DEBUG    [Thread-3 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 13:44:51,243.243] DEBUG    [Thread-3 (<lambda>)|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/QQLogin/GetQQLoginInfo HTTP/1.1" 200 124
[2025-08-28 13:44:51,243.243] INFO     [Thread-3 (<lambda>)|MainProcess] Logger (client.py:run:319) | NapCat 服务启动登录完成
[2025-08-28 13:44:51,244.244] DEBUG    [Thread-3 (<lambda>)|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 13:44:51,244.244] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (client.py:info_subscribe_message_types:250) | 已订阅消息类型:[群聊]全部消息类型
[2025-08-28 13:44:51,245.245] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (client.py:info_subscribe_message_types:251) | 已订阅消息类型:[私聊]全部消息类型
[2025-08-28 13:44:51,245.245] INFO     [Thread-3 (<lambda>)|MainProcess] PluginLoader (loader.py:load_plugins:474) | 插件目录: F:\soft\python\qq\napcat\NapCat.34740.Shell\plugins 不存在......跳过加载插件
[2025-08-28 13:44:51,247.247] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:44:51,247.247] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /event HTTP/1.1
[2025-08-28 13:44:51,247.247] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:44:51,247.247] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:44:51,247.247] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:44:51,247.247] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: KdlzSidTdLhzG5QrtJGXXA==
[2025-08-28 13:44:51,247.247] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:44:51,247.247] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:44:51,247.247] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:44:51,248.248] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:44:51,248.248] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:44:51,248.248] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:44:51,248.248] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:44:51,248.248] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: OROyX3SU9iNm+WD2DLCzQHCdEXU=
[2025-08-28 13:44:51,248.248] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:44:51,248.248] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359891,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 13:44:51,249.249] INFO     [Thread-3 (<lambda>)|MainProcess] Logger (connect.py:on_message:43) | 机器人 3292105955 成功启动
[2025-08-28 13:44:57,110.110] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359897,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:44:57,111.111] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359897, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:45:27,123.123] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359927,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:45:27,123.123] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359927, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:45:57,138.138] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359957,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:45:57,139.139] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359957, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:46:27,155.155] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756359987,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:46:27,155.155] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756359987, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:46:57,171.171] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756360017,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:46:57,171.171] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756360017, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:47:27,187.187] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756360047,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:47:27,187.187] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756360047, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:47:43,385.385] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":1420937534,"tim...roup_name":"芜湖兼职群5元1单"}' [458 bytes]
[2025-08-28 13:47:43,386.386] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'message_id': '255054335', 'message_seq': '255054335', 'real_id': '255054335', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:47:43,386.386] INFO     [Thread-3 (<lambda>)|MainProcess] Logger (main.py:on_group_message:29) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'message_id': '255054335', 'message_seq': '255054335', 'real_id': '255054335', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:47:43,389.389] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:47:43,389.389] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:47:43,389.389] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:47:43,389.389] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:47:43,389.389] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:47:43,389.389] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: MGzhca2IFQ8oWbuSYDnveQ==
[2025-08-28 13:47:43,389.389] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:47:43,389.389] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:47:43,389.389] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:47:43,389.389] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:47:43,390.390] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:47:43,390.390] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:47:43,390.390] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:47:43,390.390] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: daGc8MWvn1f4HKWIwX0dZKhDToM=
[2025-08-28 13:47:43,390.390] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:47:43,390.390] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756360063}' [185 bytes]
[2025-08-28 13:47:43,627.627] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [482 bytes]
[2025-08-28 13:47:43,627.627] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '111', 'message_id': '1678421693', 'message_seq': '1678421693', 'real_id': '1678421693', 'message': "[{'type': 'text', 'data': {'text': '111'}}]", 'message_format': 'array'}
[2025-08-28 13:47:43,627.627] INFO     [Thread-3 (<lambda>)|MainProcess] Logger (main.py:on_group_message:29) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '111', 'message_id': '1678421693', 'message_seq': '1678421693', 'real_id': '1678421693', 'message': "[{'type': 'text', 'data': {'text': '111'}}]", 'message_format': 'array'}
[2025-08-28 13:47:43,628.628] INFO     [Thread-3 (<lambda>)|MainProcess] Logger (main.py:on_group_message:50) | 处理时间: 0.001001
[2025-08-28 13:47:43,762.762] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756360063}' [103 bytes]
[2025-08-28 13:47:43,762.762] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:47:43,762.762] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:47:43,762.762] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:47:43,762.762] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:47:43,763.763] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 330985572}, 'message': '', 'wording': '', 'echo': 1756360063}
[2025-08-28 13:47:43,763.763] INFO     [Thread-3 (<lambda>)|MainProcess] Logger (main.py:on_group_message:47) | 在群 666732394 中触发关键词 '你好'，回复: 1
[2025-08-28 13:47:43,763.763] INFO     [Thread-3 (<lambda>)|MainProcess] Logger (main.py:on_group_message:50) | 处理时间: 0.376585
[2025-08-28 13:47:57,201.201] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756360077,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:47:57,202.202] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756360077, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:48:27,207.207] DEBUG    [Thread-3 (<lambda>)|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756360107,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:48:27,207.207] DEBUG    [Thread-3 (<lambda>)|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756360107, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
[2025-08-28 13:55:04,483.483] DEBUG    [ThreadPoolExecutor-0_4|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.whrstudio.top:443
[2025-08-28 13:55:04,483.483] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghfast.top:443
[2025-08-28 13:55:04,484.484] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): proxy.yaoyaoling.net:443
[2025-08-28 13:55:04,485.485] DEBUG    [ThreadPoolExecutor-0_3|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.ygxz.in:443
[2025-08-28 13:55:04,486.486] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): ghproxy.net:443
[2025-08-28 13:55:04,487.487] DEBUG    [ThreadPoolExecutor-0_1|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): github.7boe.top:443
[2025-08-28 13:55:04,487.487] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): cdn.moran233.xyz:443
[2025-08-28 13:55:04,488.488] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): fastgit.cc:443
[2025-08-28 13:55:04,489.489] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): git.886.be:443
[2025-08-28 13:55:04,489.489] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:1022) | Starting new HTTPS connection (1): gh-proxy.com:443
[2025-08-28 13:55:04,627.627] DEBUG    [ThreadPoolExecutor-0_2|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://cdn.moran233.xyz:443 "GET / HTTP/1.1" 404 19
[2025-08-28 13:55:04,628.628] WARNING  [MainThread|MainProcess] Logger (config.py:validate_config:173) | 建议设置好 root 账号保证权限功能能够正常使用
[2025-08-28 13:55:04,628.628] INFO     [MainThread|MainProcess] Logger (config.py:validate_config:174) | [BOTQQ]: 3292105955 | [WSURI]: ws://localhost:3001 | [WS_TOKEN]:  | [ROOT]: 123456 | [WEBUI]: http://localhost:6099
[2025-08-28 13:55:04,629.629] INFO     [MainThread|MainProcess] Logger (config.py:validate_config:186) | 未启用插件白名单或黑名单，将加载所有插件
[2025-08-28 13:55:04,629.629] DEBUG    [MainThread|MainProcess] AccessController (access_controller.py:_load_access:172) | 加载权限
[2025-08-28 13:55:04,629.629] WARNING  [MainThread|MainProcess] AccessController (access_controller.py:_load_access:178) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 13:55:04,629.629] WARNING  [MainThread|MainProcess] AccessController (access_controller.py:_load_access:183) | 权限文件不存在, 将创建新的权限文件
[2025-08-28 13:55:04,630.630] DEBUG    [MainThread|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 13:55:04,633.633] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:55:04,634.634] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:114) | > GET / HTTP/1.1
[2025-08-28 13:55:04,634.634] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:55:04,634.634] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:55:04,634.634] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:55:04,634.634] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: p0KzWgn7Id0qJVRM9jI/Rw==
[2025-08-28 13:55:04,634.634] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:55:04,634.634] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:55:04,634.634] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:55:04,634.634] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:55:04,635.635] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:55:04,635.635] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:55:04,635.635] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:55:04,635.635] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: 6vallygKg7wJFVulGF1uw5OB2Jg=
[2025-08-28 13:55:04,635.635] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:55:04,635.635] DEBUG    [MainThread|MainProcess] Logger (wsroute.py:check_websocket:29) | WebSocket ws://localhost:3001 可用.
[2025-08-28 13:55:04,636.636] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:55:04,636.636] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:55:04,636.636] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756360504,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 13:55:04,636.636] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:55:04,637.637] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:55:04,638.638] INFO     [MainThread|MainProcess] adapter.nc.launcher (launcher.py:ncatbot_service_remote_start:51) | napcat 服务器 ws://localhost:3001 在线, 连接中...
[2025-08-28 13:55:04,649.649] DEBUG    [ThreadPoolExecutor-0_5|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://proxy.yaoyaoling.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:55:04,659.659] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 13:55:04,933.933] WARNING  [ThreadPoolExecutor-0_4|MainProcess] urllib3.connection (connection.py:_match_hostname:547) | Certificate did not match expected hostname: github.whrstudio.top. Certificate: {'subject': ((('commonName', 'apiink.app'),),), 'issuer': ((('countryName', 'US'),), (('organizationName', "Let's Encrypt"),), (('commonName', 'R12'),)), 'version': 3, 'serialNumber': '054387AEE3DA904AB82447B5B9FEC2C87D90', 'notBefore': 'Aug 21 15:45:48 2025 GMT', 'notAfter': 'Nov 19 15:45:47 2025 GMT', 'subjectAltName': (('DNS', 'apiink.app'), ('DNS', 'www.apiink.app')), 'caIssuers': ('http://r12.i.lencr.org/',), 'crlDistributionPoints': ('http://r12.c.lencr.org/17.crl',)}
[2025-08-28 13:55:05,120.120] DEBUG    [ThreadPoolExecutor-0_8|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://git.886.be:443 "GET / HTTP/1.1" 522 0
[2025-08-28 13:55:05,125.125] DEBUG    [ThreadPoolExecutor-0_9|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://gh-proxy.com:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:55:05,201.201] DEBUG    [ThreadPoolExecutor-0_0|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghfast.top:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:55:05,941.941] DEBUG    [ThreadPoolExecutor-0_7|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://fastgit.cc:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:55:06,423.423] DEBUG    [ThreadPoolExecutor-0_6|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | https://ghproxy.net:443 "GET / HTTP/1.1" 200 None
[2025-08-28 13:55:06,707.707] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/auth/login HTTP/1.1" 200 315
[2025-08-28 13:55:06,728.728] DEBUG    [MainThread|MainProcess] adapter.nc.login (login.py:__init__:63) | 成功连接到 WEBUI
[2025-08-28 13:55:06,728.728] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_new_conn:246) | Starting new HTTP connection (1): localhost:6099
[2025-08-28 13:55:08,767.767] DEBUG    [MainThread|MainProcess] urllib3.connectionpool (connectionpool.py:_make_request:475) | http://localhost:6099 "POST /api/QQLogin/GetQQLoginInfo HTTP/1.1" 200 124
[2025-08-28 13:55:08,768.768] INFO     [MainThread|MainProcess] Logger (client.py:run:319) | NapCat 服务启动登录完成
[2025-08-28 13:55:08,768.768] DEBUG    [MainThread|MainProcess] asyncio (proactor_events.py:__init__:634) | Using proactor: IocpProactor
[2025-08-28 13:55:08,769.769] DEBUG    [MainThread|MainProcess] Logger (client.py:info_subscribe_message_types:250) | 已订阅消息类型:[群聊]全部消息类型
[2025-08-28 13:55:08,769.769] DEBUG    [MainThread|MainProcess] Logger (client.py:info_subscribe_message_types:251) | 已订阅消息类型:[私聊]全部消息类型
[2025-08-28 13:55:08,769.769] INFO     [MainThread|MainProcess] PluginLoader (loader.py:load_plugins:474) | 插件目录: F:\soft\python\qq\napcat\NapCat.34740.Shell\plugins 不存在......跳过加载插件
[2025-08-28 13:55:08,771.771] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:55:08,771.771] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /event HTTP/1.1
[2025-08-28 13:55:08,771.771] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:55:08,771.771] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:55:08,771.771] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:55:08,771.771] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: nd6K/n+kyz5VfzBE54k+lg==
[2025-08-28 13:55:08,771.771] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:55:08,771.771] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:55:08,771.771] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:55:08,771.771] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:55:08,772.772] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:55:08,772.772] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:55:08,772.772] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:55:08,772.772] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: fYw2zVhuN8xe+d+FlQNh8+bOf/c=
[2025-08-28 13:55:08,772.772] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:55:08,772.772] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756360508,"self_id":3292105955,"post_t...","sub_type":"connect"}' [116 bytes]
[2025-08-28 13:55:08,772.772] INFO     [MainThread|MainProcess] Logger (connect.py:on_message:43) | 机器人 3292105955 成功启动
[2025-08-28 13:55:26,241.241] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":1420937534,"tim...roup_name":"芜湖兼职群5元1单"}' [458 bytes]
[2025-08-28 13:55:26,242.242] DEBUG    [MainThread|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '1420937534', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 1420937534, 'nickname': 'zero', 'card': ''}", 'raw_message': '你好', 'message_id': '520400094', 'message_seq': '520400094', 'real_id': '520400094', 'message': "[{'type': 'text', 'data': {'text': '你好'}}]", 'message_format': 'array'}
[2025-08-28 13:55:26,244.244] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:__init__:256) | = connection is CONNECTING
[2025-08-28 13:55:26,244.244] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:114) | > GET /api HTTP/1.1
[2025-08-28 13:55:26,244.244] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Host: localhost:3001
[2025-08-28 13:55:26,244.244] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Upgrade: websocket
[2025-08-28 13:55:26,244.244] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Connection: Upgrade
[2025-08-28 13:55:26,244.244] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Key: QlVZ+jA8KoA3mRRFlmB0lQ==
[2025-08-28 13:55:26,244.244] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Version: 13
[2025-08-28 13:55:26,245.245] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
[2025-08-28 13:55:26,245.245] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > Content-Type: application/json
[2025-08-28 13:55:26,245.245] DEBUG    [MainThread|MainProcess] websockets.client (client.py:write_http_request:116) | > User-Agent: Python/3.12 websockets/10.4
[2025-08-28 13:55:26,245.245] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:147) | < HTTP/1.1 101 Switching Protocols
[2025-08-28 13:55:26,245.245] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Upgrade: websocket
[2025-08-28 13:55:26,245.245] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Connection: Upgrade
[2025-08-28 13:55:26,245.245] DEBUG    [MainThread|MainProcess] websockets.client (client.py:read_http_response:149) | < Sec-WebSocket-Accept: LGs81CK8awt/lPVVFhzT+kPkF1I=
[2025-08-28 13:55:26,245.245] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:connection_open:357) | = connection is OPEN
[2025-08-28 13:55:26,245.245] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > TEXT '{"action": "send_group_msg", "params": {"group_...]}, "echo": 1756360526}' [185 bytes]
[2025-08-28 13:55:26,425.425] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"self_id":3292105955,"user_id":3236117226,"tim...roup_name":"芜湖兼职群5元1单"}' [479 bytes]
[2025-08-28 13:55:26,425.425] DEBUG    [MainThread|MainProcess] Logger (client.py:handle_group_event:166) | {'group_id': '666732394', 'user_id': '3236117226', 'message_type': 'group', 'sub_type': 'normal', 'font': '14', 'sender': "{'user_id': 3236117226, 'nickname': '接插件定制/抢购秒发等', 'card': ''}", 'raw_message': '111', 'message_id': '303730914', 'message_seq': '303730914', 'real_id': '303730914', 'message': "[{'type': 'text', 'data': {'text': '111'}}]", 'message_format': 'array'}
[2025-08-28 13:55:26,616.616] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"status":"ok","retcode":0,"data":{"message_id"...":"","echo":1756360526}' [103 bytes]
[2025-08-28 13:55:26,616.616] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:write_close_frame:1232) | = connection is CLOSING
[2025-08-28 13:55:26,616.616] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:write_frame_sync:1183) | > CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:55:26,617.617] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < CLOSE 1000 (OK) [2 bytes]
[2025-08-28 13:55:26,617.617] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:connection_lost:1514) | = connection is CLOSED
[2025-08-28 13:55:26,617.617] DEBUG    [MainThread|MainProcess] Logger (function_enhance.py:check_and_log:76) | {'status': 'ok', 'retcode': 0, 'data': {'message_id': 841046682}, 'message': '', 'wording': '', 'echo': 1756360526}
[2025-08-28 13:55:27,327.327] DEBUG    [MainThread|MainProcess] websockets.client (protocol.py:read_frame:1177) | < TEXT '{"time":1756360527,"self_id":3292105955,"post_t...true},"interval":30000}' [149 bytes]
[2025-08-28 13:55:27,328.328] DEBUG    [MainThread|MainProcess] Logger (connect.py:on_message:45) | {'time': 1756360527, 'self_id': 3292105955, 'post_type': 'meta_event', 'meta_event_type': 'heartbeat', 'status': {'online': True, 'good': True}, 'interval': 30000}
