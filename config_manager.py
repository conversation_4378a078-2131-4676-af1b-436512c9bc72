import json
import os
import time
from threading import Lock

CONFIG_FILE = "config.json"
DEFAULT_CONFIG = {
    "bot_uin": "382435027",  # 机器人QQ号
    "monitored_groups": [],  # 监控的群列表 [{"group_id": "123", "group_name": "测试群"}]
    "keyword_replies": {}    # 关键词回复 {"关键词1": ["回复1", "回复2"], "关键词2": ["回复3"]}
}

# 配置缓存和锁
_config_cache = None
_cache_lock = Lock()
_last_modified = 0
_cache_enabled = True  # 缓存开关，生产环境建议开启

def load_config():
    """加载配置文件（高性能缓存优化）"""
    global _config_cache, _last_modified

    # 如果禁用缓存，直接读取文件
    if not _cache_enabled:
        return _load_config_from_file()

    with _cache_lock:
        # 如果缓存存在，先返回缓存（减少文件系统调用）
        if _config_cache is not None:
            try:
                # 只在必要时检查文件修改时间
                current_modified = os.path.getmtime(CONFIG_FILE)
                if current_modified == _last_modified:
                    return _config_cache
            except OSError:
                # 文件可能被删除，重新加载
                pass

        # 需要重新加载配置
        return _load_config_from_file()

def _load_config_from_file():
    """从文件加载配置的内部函数"""
    global _config_cache, _last_modified

    # 检查文件是否存在
    if not os.path.exists(CONFIG_FILE):
        _config_cache = DEFAULT_CONFIG.copy()
        _last_modified = 0
        return _config_cache

    # 读取配置文件
    try:
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            _config_cache = json.load(f)
            _last_modified = os.path.getmtime(CONFIG_FILE)
            return _config_cache
    except Exception as e:
        print(f"加载配置文件出错: {e}")
        _config_cache = DEFAULT_CONFIG.copy()
        _last_modified = 0
        return _config_cache

def save_config(config):
    """保存配置文件（更新缓存）"""
    global _config_cache, _last_modified

    try:
        with _cache_lock:
            with open(CONFIG_FILE, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=4)

            # 更新缓存和修改时间
            _config_cache = config.copy()
            _last_modified = os.path.getmtime(CONFIG_FILE)
        return True
    except Exception as e:
        print(f"保存配置文件出错: {e}")
        return False

def set_bot_uin(config, bot_uin):
    """设置机器人QQ号"""
    config["bot_uin"] = bot_uin
    return save_config(config)

def add_monitored_group(config, group_id, group_name):
    """添加监控群"""
    for group in config["monitored_groups"]:
        if group["group_id"] == group_id:
            return False  # 已存在
    
    config["monitored_groups"].append({"group_id": group_id, "group_name": group_name})
    return save_config(config)

def remove_monitored_group(config, group_id):
    """移除监控群"""
    config["monitored_groups"] = [g for g in config["monitored_groups"] if g["group_id"] != group_id]
    return save_config(config)

def add_keyword_reply(config, keyword, reply):
    """添加关键词回复"""
    if keyword not in config["keyword_replies"]:
        config["keyword_replies"][keyword] = []
    
    if reply not in config["keyword_replies"][keyword]:
        config["keyword_replies"][keyword].append(reply)
        return save_config(config)
    return False

def remove_keyword_reply(config, keyword, reply=None):
    """移除关键词回复，如果reply为None则移除整个关键词"""
    if keyword not in config["keyword_replies"]:
        return False

    if reply is None:
        del config["keyword_replies"][keyword]
    elif reply in config["keyword_replies"][keyword]:
        config["keyword_replies"][keyword].remove(reply)
        if not config["keyword_replies"][keyword]:
            del config["keyword_replies"][keyword]
    else:
        return False

    return save_config(config)

def clear_cache():
    """清理配置缓存，强制重新加载"""
    global _config_cache, _last_modified
    with _cache_lock:
        _config_cache = None
        _last_modified = 0