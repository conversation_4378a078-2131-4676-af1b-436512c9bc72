#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
终极性能测试脚本 - 测试所有优化措施的效果
"""

import time
import asyncio
import random
import statistics
import gc
import os
import psutil
from concurrent.futures import ThreadPoolExecutor

# 设置终极性能模式
os.environ["DISABLE_DEBUG_LOGS"] = "1"
os.environ["FORCE_CACHE_MODE"] = "1"
os.environ["ENABLE_ULTRA_MODE"] = "1"

def test_memory_performance():
    """测试内存性能"""
    print("🧠 内存性能测试")
    print("-" * 40)
    
    # 获取当前进程
    process = psutil.Process()
    
    # 测试前内存使用
    memory_before = process.memory_info().rss / 1024 / 1024
    print(f"测试前内存使用: {memory_before:.1f}MB")
    
    # 创建大量对象测试
    test_objects = []
    start_time = time.time()
    
    for i in range(100000):
        test_objects.append({
            "id": i,
            "data": f"test_data_{i}",
            "timestamp": time.time()
        })
    
    creation_time = time.time() - start_time
    memory_after = process.memory_info().rss / 1024 / 1024
    
    print(f"创建10万个对象耗时: {creation_time*1000:.2f}ms")
    print(f"测试后内存使用: {memory_after:.1f}MB")
    print(f"内存增长: {memory_after - memory_before:.1f}MB")
    
    # 垃圾回收测试
    gc_start = time.time()
    gc.collect()
    gc_time = time.time() - gc_start
    
    memory_after_gc = process.memory_info().rss / 1024 / 1024
    print(f"垃圾回收耗时: {gc_time*1000:.2f}ms")
    print(f"GC后内存使用: {memory_after_gc:.1f}MB")
    
    # 清理
    del test_objects
    gc.collect()

def test_string_matching_performance():
    """测试字符串匹配性能"""
    print("\n🔍 字符串匹配性能测试")
    print("-" * 40)
    
    # 准备测试数据
    keywords = ["你好", "测试", "机器人", "帮助", "问题", "回复", "消息", "群聊"]
    test_messages = [
        "你好，请问有什么可以帮助的吗？",
        "这是一条测试消息",
        "机器人回复速度如何？",
        "群聊中的消息处理",
        "没有关键词的普通消息内容",
        "包含多个关键词：你好机器人，需要帮助"
    ]
    
    test_count = 100000
    
    # 方法1：简单in操作
    start_time = time.time()
    matches1 = 0
    for _ in range(test_count):
        message = random.choice(test_messages)
        for keyword in keywords:
            if keyword in message:
                matches1 += 1
                break
    time1 = time.time() - start_time
    
    # 方法2：预处理小写
    start_time = time.time()
    matches2 = 0
    keywords_lower = [k.lower() for k in keywords]
    for _ in range(test_count):
        message = random.choice(test_messages).lower()
        for keyword in keywords_lower:
            if keyword in message:
                matches2 += 1
                break
    time2 = time.time() - start_time
    
    # 方法3：使用集合
    start_time = time.time()
    matches3 = 0
    keyword_set = set(keywords)
    for _ in range(test_count):
        message = random.choice(test_messages)
        message_words = set(message.split())
        if keyword_set & message_words:
            matches3 += 1
    time3 = time.time() - start_time
    
    print(f"方法1 (简单in): {time1*1000:.2f}ms, 匹配{matches1}次")
    print(f"方法2 (预处理): {time2*1000:.2f}ms, 匹配{matches2}次")
    print(f"方法3 (集合交集): {time3*1000:.2f}ms, 匹配{matches3}次")
    
    best_time = min(time1, time2, time3)
    print(f"最佳方法耗时: {best_time*1000:.2f}ms")
    print(f"平均每次匹配: {best_time*1000000/test_count:.2f}μs")

async def test_async_performance():
    """测试异步性能"""
    print("\n⚡ 异步性能测试")
    print("-" * 40)
    
    async def mock_reply(delay=0.001):
        """模拟回复操作"""
        await asyncio.sleep(delay)
        return "回复完成"
    
    # 测试并发回复
    test_count = 1000
    
    # 串行处理
    start_time = time.time()
    for _ in range(test_count):
        await mock_reply()
    serial_time = time.time() - start_time
    
    # 并发处理
    start_time = time.time()
    tasks = [mock_reply() for _ in range(test_count)]
    await asyncio.gather(*tasks)
    concurrent_time = time.time() - start_time
    
    print(f"串行处理{test_count}个回复: {serial_time*1000:.2f}ms")
    print(f"并发处理{test_count}个回复: {concurrent_time*1000:.2f}ms")
    print(f"性能提升: {serial_time/concurrent_time:.1f}倍")

def test_config_loading_performance():
    """测试配置加载性能"""
    print("\n📁 配置加载性能测试")
    print("-" * 40)
    
    import config_manager
    
    # 清理缓存
    config_manager.clear_cache()
    
    # 首次加载（从文件）
    start_time = time.time()
    config1 = config_manager.load_config()
    first_load_time = time.time() - start_time
    
    # 缓存加载
    times = []
    for _ in range(1000):
        start_time = time.time()
        config = config_manager.load_config()
        times.append(time.time() - start_time)
    
    avg_cache_time = statistics.mean(times)
    min_cache_time = min(times)
    max_cache_time = max(times)
    
    print(f"首次加载耗时: {first_load_time*1000:.3f}ms")
    print(f"缓存加载平均: {avg_cache_time*1000000:.2f}μs")
    print(f"缓存加载最快: {min_cache_time*1000000:.2f}μs")
    print(f"缓存加载最慢: {max_cache_time*1000000:.2f}μs")
    print(f"缓存性能提升: {first_load_time/avg_cache_time:.0f}倍")

def test_end_to_end_performance():
    """端到端性能测试"""
    print("\n🎯 端到端性能测试")
    print("-" * 40)
    
    try:
        from main import QQrobot
        
        # 创建机器人实例
        robot = QQrobot()
        
        # 模拟消息
        class MockMessage:
            def __init__(self, group_id, raw_message):
                self.group_id = group_id
                self.raw_message = raw_message
            
            async def reply(self, text):
                pass
        
        # 测试数据
        test_messages = [
            MockMessage(666732394, "你好"),  # 匹配
            MockMessage(666732394, "测试消息"),  # 不匹配
            MockMessage(999999999, "你好"),  # 非监控群
            MockMessage(666732394, ""),  # 空消息
        ]
        
        # 性能测试
        test_count = 10000
        times = []
        matches = 0
        
        for _ in range(test_count):
            msg = random.choice(test_messages)
            
            start_time = time.time()
            
            # 模拟处理逻辑
            if msg.group_id in robot.monitored_group_ids:
                if msg.raw_message:
                    message_lower = msg.raw_message.lower()
                    for keyword_data in robot.keyword_cache:
                        if keyword_data["keyword"] in message_lower:
                            matches += 1
                            break
            
            process_time = time.time() - start_time
            times.append(process_time)
        
        avg_time = statistics.mean(times) * 1000000  # 转换为微秒
        min_time = min(times) * 1000000
        max_time = max(times) * 1000000
        
        print(f"处理{test_count}条消息")
        print(f"平均处理时间: {avg_time:.2f}μs")
        print(f"最快处理时间: {min_time:.2f}μs")
        print(f"最慢处理时间: {max_time:.2f}μs")
        print(f"匹配成功: {matches}次")
        print(f"匹配率: {matches/test_count*100:.1f}%")
        
        # 性能评级
        if avg_time < 10:
            print("🏆 性能评级: 优秀")
        elif avg_time < 50:
            print("🥇 性能评级: 良好")
        elif avg_time < 100:
            print("🥈 性能评级: 一般")
        else:
            print("🥉 性能评级: 需要优化")
            
    except Exception as e:
        print(f"❌ 端到端测试失败: {e}")

def test_system_performance():
    """测试系统性能"""
    print("\n💻 系统性能测试")
    print("-" * 40)
    
    # CPU信息
    cpu_count = psutil.cpu_count()
    cpu_freq = psutil.cpu_freq()
    print(f"CPU核心数: {cpu_count}")
    if cpu_freq:
        print(f"CPU频率: {cpu_freq.current:.0f}MHz")
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total/1024/1024/1024:.1f}GB")
    print(f"可用内存: {memory.available/1024/1024/1024:.1f}GB")
    print(f"内存使用率: {memory.percent:.1f}%")
    
    # 磁盘信息
    disk = psutil.disk_usage('.')
    print(f"磁盘总空间: {disk.total/1024/1024/1024:.1f}GB")
    print(f"磁盘可用空间: {disk.free/1024/1024/1024:.1f}GB")
    
    # 网络延迟测试
    import socket
    try:
        start_time = time.time()
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(("127.0.0.1", 3001))
        sock.close()
        latency = (time.time() - start_time) * 1000
        
        if result == 0:
            print(f"本地网络延迟: {latency:.2f}ms")
        else:
            print("本地网络: 连接失败")
    except:
        print("本地网络: 测试失败")

async def main():
    """主测试函数"""
    print("🚀 QQ机器人终极性能测试")
    print("=" * 60)
    
    # 系统性能测试
    test_system_performance()
    
    # 内存性能测试
    test_memory_performance()
    
    # 字符串匹配性能测试
    test_string_matching_performance()
    
    # 异步性能测试
    await test_async_performance()
    
    # 配置加载性能测试
    test_config_loading_performance()
    
    # 端到端性能测试
    test_end_to_end_performance()
    
    print("\n" + "=" * 60)
    print("🎉 终极性能测试完成!")
    print("\n💡 优化建议:")
    print("1. 如果平均处理时间 > 50μs，考虑进一步优化算法")
    print("2. 如果内存使用 > 500MB，考虑减少缓存大小")
    print("3. 如果网络延迟 > 10ms，检查网络配置")
    print("4. 定期运行此测试以监控性能变化")

if __name__ == "__main__":
    asyncio.run(main())
