{"network": {"websocketServers": [{"name": "WsServer", "enable": true, "host": "127.0.0.1", "port": 3001, "messagePostFormat": "array", "reportSelfMessage": false, "token": "", "enableForcePushEvent": true, "debug": false, "heartInterval": 10000, "maxConnections": 100, "compression": true, "perMessageDeflate": true, "maxPayload": 1048576, "pingTimeout": 5000, "pongTimeout": 3000}], "httpServers": [{"name": "HttpServer", "enable": false, "host": "127.0.0.1", "port": 3000, "enableCors": false, "enableWebsocket": false, "messagePostFormat": "array", "token": "", "debug": false}]}, "musicSignUrl": "", "enableLocalFile2Url": false, "parseMultMsg": false, "enableQQNTIM": true, "reportSelfMessage": false, "enableForcePushEvent": true, "performance": {"enableMessageQueue": true, "messageQueueSize": 1000, "enableBatchSend": true, "batchSize": 10, "batchTimeout": 100, "enableConnectionPool": true, "connectionPoolSize": 20, "enableKeepAlive": true, "keepAliveTimeout": 30000, "enableCompression": true, "compressionLevel": 6, "enableCache": true, "cacheSize": 1000, "cacheTTL": 300000}}