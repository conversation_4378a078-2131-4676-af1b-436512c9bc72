08-28 14:08:25 [info] [StdOut] 已重定向到命名管道: \\.\pipe\NapCat_30424
08-28 14:08:25 [info] [NapCat] [Core] NapCat.Core Version: 4.8.102
08-28 14:08:25 [info] [Check] ffmpeg: F:\soft\ffmpeg\bin\ffmpeg.exe
08-28 14:08:25 [info] [Check] ffprobe: F:\soft\ffmpeg\bin\ffprobe.exe
08-28 14:08:25 [info] 等待网络连接...
08-28 14:08:25 [info] [NapCat] [WebUi] WebUi Local Panel Url: http://127.0.0.1:6099/webui?token=napcat
08-28 14:08:26 [info] 网络已连接
08-28 14:08:26 [info] 没有 -q 指令指定快速登录，将使用二维码登录方式
08-28 14:08:26 [info] 可用于快速登录的 QQ：
1. 1420937534 瓦特羊
2. 382435027 大龙
08-28 14:08:26 [warn] 请扫描下面的二维码，然后在手Q上授权登录：
08-28 14:08:26 [warn] 

▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄
█ ▄▄▄▄▄ █▄▄▄ ▀▄██ █ ▀█ ▄▄▀█ ▄▄▄▄▄ █
█ █   █ ██▄▀ █ ▀▄▄▀██▄▄█▄▀█ █   █ █
█ █▄▄▄█ ██▀▄ ▄████▄▄▄▄██ ██ █▄▄▄█ █
█▄▄▄▄▄▄▄█ ▀▄█ ▀ █ █ ▀ █▄▀ █▄▄▄▄▄▄▄█
█▄▄▀▄▄▀▄██▄▀█▄██▀▀ ▀█▄█▀▀▀█▄▀▀█▀▀▄█
█▄▄▀ ▀▀▄▄▀▄██▄█▄ █▀▀█▀█  ▀▀  ▄▀▀▄▄█
█▄▄▄█ █▄▄▄  █▀▀▄   ▀▄▄▀██▄▀▄▄███▄▄█
██▄▀█ ▄▄▀▀▀██▀▄▀ ▄██ ▀▄▀█▀▀▄█ █▀█▀█
█  █▀█▀▄▄▄ ▄ ▄▄  ▀██▀ ▀▀█▀▄ ▀ ▄█  █
█▄▄▄▄▀█▄█   ▀▄   █▄ ▄▀█▄▄██▀█▄█▀▀ █
█ ▄ ▀▀█▄███▄▄▀██▀▄▀█▀█▄█ ▀▄█▀▀▀█▀██
█ █ ██ ▄█▄▄▄ ▀█ ▄█ ▄▀ ▄▀▄▀ ▀ ▄█▀▄▄█
█▄▄███▄▄█▀█▀▄▄▀▄█ ▀▀▀▄█ █ ▄▄▄ ▀▀ ██
█ ▄▄▄▄▄ ██▀▄ ▄█ ▄██▀ ▀▄▀  █▄█ ▀ █▀█
█ █   █ █ ██▀ ▄ ▀█▄▀ ▀██▄▄ ▄▄▄▀█▄▄█
█ █▄▄▄█ █▀ ▄▄▄  ▄▄▄▄▄ ▄▀▀█▀▀▄   ▀ █
█▄▄▄▄▄▄▄█▄█▄████▄█▄█▄█▄██▄▄█▄█▄████

二维码解码URL: https://txz.qq.com/p?k=9I3FCLfl*P45BzNkbeX7ifnMxYlIpmLk&f=1600001604
如果控制台二维码无法扫码，可以复制解码url到二维码生成网站生成二维码再扫码，也可以打开下方的二维码路径图片进行扫码。
08-28 14:08:26 [warn] 二维码已保存到 F:\soft\python\qq\napcat\NapCat.34740.Shell\versions\9.9.19-34740\resources\app\napcat\cache\qrcode.png
08-28 14:08:47 [debug] 本账号数据/缓存目录： C:\Users\<USER>\Documents\Tencent Files\NapCat\data
08-28 14:08:47 [debug] [Core] [Config] 配置文件F:\soft\python\qq\napcat\NapCat.34740.Shell\versions\9.9.19-34740\resources\app\napcat\config\napcat.json加载 {"fileLog":false,"consoleLog":true,"fileLogLevel":"debug","consoleLogLevel":"info","packetBackend":"auto","packetServer":"","o3HookMode":1}
08-28 14:08:47 [info] [Core] [Packet] 自动选择 NativePacketClient 作为后端
