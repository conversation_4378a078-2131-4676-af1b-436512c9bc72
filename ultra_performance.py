#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
终极性能优化模块 - 针对QQ机器人回复速度的极致优化
"""

import asyncio
import time
import threading
from collections import deque
from concurrent.futures import ThreadPoolExecutor
import weakref
import gc

class MessagePool:
    """消息对象池，减少对象创建开销"""
    
    def __init__(self, max_size=1000):
        self.pool = deque(maxlen=max_size)
        self.lock = threading.Lock()
    
    def get_message_handler(self):
        """获取消息处理器"""
        with self.lock:
            if self.pool:
                return self.pool.popleft()
            return self._create_handler()
    
    def return_handler(self, handler):
        """归还消息处理器"""
        with self.lock:
            if len(self.pool) < self.pool.maxlen:
                handler.reset()
                self.pool.append(handler)
    
    def _create_handler(self):
        """创建新的处理器"""
        return MessageHandler()

class MessageHandler:
    """可重用的消息处理器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置处理器状态"""
        self.group_id = None
        self.message = None
        self.matched_keyword = None
        self.reply_text = None
    
    def process(self, group_id, message, keyword_cache, monitored_groups):
        """处理消息"""
        self.group_id = group_id
        self.message = message
        
        # 快速群检查
        if group_id not in monitored_groups:
            return False
        
        # 快速关键词匹配
        if not message or len(message) > 200:
            return False
        
        message_lower = message.lower()
        
        # 优化的匹配算法
        for keyword_data in keyword_cache:
            if keyword_data["keyword"] in message_lower:
                self.matched_keyword = keyword_data["original_keyword"]
                self.reply_text = keyword_data["reply"]
                return True
        
        return False

class AsyncReplyQueue:
    """异步回复队列，批量处理回复请求"""
    
    def __init__(self, max_workers=5, batch_size=10):
        self.queue = asyncio.Queue(maxsize=1000)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.batch_size = batch_size
        self.running = False
        self.stats = {
            "total_replies": 0,
            "failed_replies": 0,
            "avg_response_time": 0.0
        }
    
    async def start(self):
        """启动回复处理器"""
        self.running = True
        asyncio.create_task(self._process_replies())
    
    async def stop(self):
        """停止回复处理器"""
        self.running = False
    
    async def add_reply(self, msg, reply_text, priority=1):
        """添加回复到队列"""
        try:
            await self.queue.put({
                "msg": msg,
                "reply_text": reply_text,
                "priority": priority,
                "timestamp": time.time()
            })
        except asyncio.QueueFull:
            # 队列满时丢弃低优先级消息
            pass
    
    async def _process_replies(self):
        """处理回复队列"""
        batch = []
        
        while self.running:
            try:
                # 收集批量回复
                while len(batch) < self.batch_size and self.running:
                    try:
                        reply_item = await asyncio.wait_for(
                            self.queue.get(), timeout=0.1
                        )
                        batch.append(reply_item)
                    except asyncio.TimeoutError:
                        break
                
                # 批量发送回复
                if batch:
                    await self._send_batch_replies(batch)
                    batch.clear()
                
            except Exception as e:
                print(f"回复处理错误: {e}")
                batch.clear()
    
    async def _send_batch_replies(self, batch):
        """批量发送回复"""
        tasks = []
        
        for reply_item in batch:
            task = asyncio.create_task(
                self._send_single_reply(reply_item)
            )
            tasks.append(task)
        
        # 并发发送所有回复
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _send_single_reply(self, reply_item):
        """发送单个回复"""
        start_time = time.time()
        
        try:
            msg = reply_item["msg"]
            reply_text = reply_item["reply_text"]
            
            await msg.reply(text=reply_text)
            
            # 更新统计
            self.stats["total_replies"] += 1
            response_time = time.time() - start_time
            self.stats["avg_response_time"] = (
                self.stats["avg_response_time"] * 0.9 + response_time * 0.1
            )
            
        except Exception as e:
            self.stats["failed_replies"] += 1
            print(f"发送回复失败: {e}")

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {
            "messages_processed": 0,
            "messages_matched": 0,
            "avg_processing_time": 0.0,
            "peak_memory_usage": 0,
            "gc_count": 0
        }
        self.start_time = time.time()
        self.last_gc_time = time.time()
    
    def record_message(self, processing_time, matched=False):
        """记录消息处理指标"""
        self.metrics["messages_processed"] += 1
        if matched:
            self.metrics["messages_matched"] += 1
        
        # 更新平均处理时间（指数移动平均）
        self.metrics["avg_processing_time"] = (
            self.metrics["avg_processing_time"] * 0.95 + 
            processing_time * 0.05
        )
    
    def check_memory(self):
        """检查内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_mb = process.memory_info().rss / 1024 / 1024
        
        if memory_mb > self.metrics["peak_memory_usage"]:
            self.metrics["peak_memory_usage"] = memory_mb
        
        # 如果内存使用过高，触发垃圾回收
        if memory_mb > 500:  # 500MB阈值
            current_time = time.time()
            if current_time - self.last_gc_time > 60:  # 1分钟间隔
                gc.collect()
                self.metrics["gc_count"] += 1
                self.last_gc_time = current_time
    
    def get_stats(self):
        """获取性能统计"""
        uptime = time.time() - self.start_time
        
        return {
            **self.metrics,
            "uptime_seconds": uptime,
            "messages_per_second": self.metrics["messages_processed"] / max(uptime, 1),
            "match_rate": (
                self.metrics["messages_matched"] / 
                max(self.metrics["messages_processed"], 1) * 100
            )
        }

class UltraPerformanceBot:
    """终极性能优化的机器人类"""
    
    def __init__(self, original_bot):
        self.original_bot = original_bot
        self.message_pool = MessagePool()
        self.reply_queue = AsyncReplyQueue()
        self.monitor = PerformanceMonitor()
        
        # 缓存引用
        self.monitored_groups = original_bot.monitored_group_ids
        self.keyword_cache = original_bot.keyword_cache
    
    async def start(self):
        """启动优化组件"""
        await self.reply_queue.start()
    
    async def stop(self):
        """停止优化组件"""
        await self.reply_queue.stop()
    
    async def process_message_ultra_fast(self, msg):
        """超高速消息处理"""
        start_time = time.time()
        
        try:
            # 从对象池获取处理器
            handler = self.message_pool.get_message_handler()
            
            # 处理消息
            matched = handler.process(
                msg.group_id, 
                msg.raw_message,
                self.keyword_cache,
                self.monitored_groups
            )
            
            if matched:
                # 异步发送回复
                await self.reply_queue.add_reply(msg, handler.reply_text)
            
            # 归还处理器
            self.message_pool.return_handler(handler)
            
            # 记录性能指标
            processing_time = time.time() - start_time
            self.monitor.record_message(processing_time, matched)
            
            # 定期检查内存
            if self.monitor.metrics["messages_processed"] % 100 == 0:
                self.monitor.check_memory()
            
        except Exception as e:
            print(f"消息处理错误: {e}")
    
    def get_performance_stats(self):
        """获取性能统计"""
        bot_stats = self.monitor.get_stats()
        reply_stats = self.reply_queue.stats
        
        return {
            "bot": bot_stats,
            "reply_queue": reply_stats,
            "optimization_level": "ULTRA"
        }

def create_ultra_performance_wrapper(original_bot):
    """创建终极性能包装器"""
    return UltraPerformanceBot(original_bot)

# 使用示例
async def demo_ultra_performance():
    """演示终极性能优化"""
    print("终极性能优化演示")
    
    # 模拟原始机器人
    class MockBot:
        def __init__(self):
            self.monitored_group_ids = {666732394}
            self.keyword_cache = [
                {"keyword": "你好", "reply": "1", "original_keyword": "你好"}
            ]
    
    original_bot = MockBot()
    ultra_bot = create_ultra_performance_wrapper(original_bot)
    
    await ultra_bot.start()
    
    # 模拟高频消息处理
    class MockMessage:
        def __init__(self, group_id, raw_message):
            self.group_id = group_id
            self.raw_message = raw_message
        
        async def reply(self, text):
            pass
    
    # 性能测试
    start_time = time.time()
    test_count = 10000
    
    for i in range(test_count):
        msg = MockMessage(666732394, f"你好{i}")
        await ultra_bot.process_message_ultra_fast(msg)
    
    end_time = time.time()
    
    print(f"处理{test_count}条消息耗时: {(end_time-start_time)*1000:.2f}ms")
    print(f"平均每条消息: {(end_time-start_time)*1000000/test_count:.2f}μs")
    
    # 显示性能统计
    stats = ultra_bot.get_performance_stats()
    print("性能统计:", stats)
    
    await ultra_bot.stop()

if __name__ == "__main__":
    asyncio.run(demo_ultra_performance())
