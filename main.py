# ========= 导入必要模块 ==========
from ncatbot.core import BotClient, GroupMessage, PrivateMessage
from ncatbot.utils import get_log
from ncatbot.utils import config
import random,time
import config_manager

class QQrobot:  
    def __init__(self, bot_uin="382435027"):
        config.set_bot_uin(bot_uin)  # 设置 bot qq 号 (必填)

        config.set_ws_uri("ws://localhost:3001")  # 设置 napcat websocket server 地址
        config.set_ws_token("")  # 设置 token (websocket 的 token)
        config.set_webui_uri("http://localhost:6099")  # 设置 napcat webui 地址
        config.set_webui_token("napcat")  # 设置 token (webui 的 token)

        # ========== 创建 BotClient ==========
        self.bot = BotClient()
        self._log = get_log()

        # 加载配置文件
        self.bot_config = config_manager.load_config()

        # ========= 注册回调函数 ==========
        @self.bot.group_event()
        async def on_group_message(msg: GroupMessage):
            start=time.time()
            self._log.info(msg)
            
            # # 测试命令
            # if msg.raw_message == "测试":
            #     await msg.reply(text="NcatBot 测试成功喵~")
            #     return
            
            # 检查是否是监控的群
            is_monitored = False
            for group in self.bot_config["monitored_groups"]:
                if str(msg.group_id) == str(group["group_id"]):
                    is_monitored = True
                    break
            
            if not is_monitored:
                return
            
            # 检查消息中是否包含关键词
            for keyword, replies in self.bot_config["keyword_replies"].items():
                if keyword in msg.raw_message and replies:
                    # 随机选择一个回复
                    reply = random.choice(replies)
                    await msg.reply(text=reply)
                    self._log.info(f"在群 {msg.group_id} 中触发关键词 '{keyword}'，回复: {reply}")
                    break
            end_time=time.time()
            self._log.info(f"处理时间: {end_time - start:.6f}")

        @self.bot.private_event()
        async def on_private_message(msg: PrivateMessage):
            self._log.info(msg)
            if msg.raw_message == "测试":
                await self.bot.api.post_private_msg(msg.user_id, text="NcatBot 测试成功喵~")
            elif msg.raw_message == "重载配置":
                self.bot_config = config_manager.load_config()
                await self.bot.api.post_private_msg(msg.user_id, text="配置重载成功！")
    
    def run(self):
        self.bot.run()

def bot_run(bot_uin=None):
    # 如果未指定QQ号，尝试从配置文件中获取
    if bot_uin is None:
        bot_config = config_manager.load_config()
        bot_uin = bot_config.get("bot_uin", "382435027")
        
    robot = QQrobot(bot_uin)
    robot.run()

# ========== 启动 BotClient==========
if __name__ == "__main__":
    bot_run()