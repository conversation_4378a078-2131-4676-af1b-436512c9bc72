# ========= 导入必要模块 ==========
from ncatbot.core import BotClient, GroupMessage, PrivateMessage
from ncatbot.utils import get_log
from ncatbot.utils import config
import config_manager
import os

# 性能优化配置 - 从环境变量或配置文件读取
ENABLE_DEBUG_LOGS = os.getenv("DISABLE_DEBUG_LOGS") != "1"
ENABLE_TIMING_LOGS = False  # 生产环境建议关闭
FORCE_CACHE_MODE = os.getenv("FORCE_CACHE_MODE") == "1"

class QQrobot:
    def __init__(self, bot_uin="382435027"):
        config.set_bot_uin(bot_uin)  # 设置 bot qq 号 (必填)

        config.set_ws_uri("ws://localhost:3001")  # 设置 napcat websocket server 地址
        config.set_ws_token("")  # 设置 token (websocket 的 token)
        config.set_webui_uri("http://localhost:6099")  # 设置 napcat webui 地址
        config.set_webui_token("napcat")  # 设置 token (webui 的 token)

        # ========== 创建 BotClient ==========
        self.bot = BotClient()
        self._log = get_log()

        # 加载配置文件并缓存监控群ID集合和关键词
        self.bot_config = config_manager.load_config()
        self._update_caches()

        # ========= 注册回调函数 ==========
        @self.bot.group_event()
        async def on_group_message(msg: GroupMessage):
            # 性能优化：条件性日志记录
            if ENABLE_DEBUG_LOGS:
                self._log.info(msg)

            # 快速群检查（第一层过滤）
            if msg.group_id not in self.monitored_group_ids:
                return

            # 快速关键词匹配（第二层过滤）
            message = msg.raw_message
            if not message:  # 空消息快速跳过
                return

            # 使用预编译的关键词缓存进行快速匹配
            for keyword_data in self.keyword_cache:
                if keyword_data["keyword"] in message:
                    # 直接使用预选的回复，最小化延迟
                    await msg.reply(text=keyword_data["reply"])

                    # 条件性日志记录
                    if ENABLE_DEBUG_LOGS:
                        self._log.info(f"群 {msg.group_id} 触发关键词 '{keyword_data['keyword']}'")
                    return  # 找到匹配后立即返回，避免继续检查

        @self.bot.private_event()
        async def on_private_message(msg: PrivateMessage):
            self._log.info(msg)
            if msg.raw_message == "测试":
                await self.bot.api.post_private_msg(msg.user_id, text="NcatBot 测试成功喵~")
            elif msg.raw_message == "重载配置":
                self.bot_config = config_manager.load_config()
                self._update_caches()  # 重载配置后更新所有缓存
                await self.bot.api.post_private_msg(msg.user_id, text="配置重载成功！")

    def _update_caches(self):
        """更新所有缓存，最大化性能"""
        # 缓存监控群ID（使用int类型，避免字符串转换）
        self.monitored_group_ids = {int(group["group_id"]) for group in self.bot_config["monitored_groups"]}

        # 高性能关键词缓存策略
        self.keyword_cache = []
        keyword_replies = self.bot_config["keyword_replies"]

        if keyword_replies:
            import random
            # 预编译关键词缓存，避免运行时的随机选择和字典查找
            for keyword, replies in keyword_replies.items():
                if replies:  # 只缓存有回复的关键词
                    # 预选一个回复，避免运行时随机选择
                    selected_reply = random.choice(replies)
                    self.keyword_cache.append({
                        "keyword": keyword,
                        "reply": selected_reply,
                        "keyword_len": len(keyword)  # 预计算长度
                    })

            # 按关键词长度排序，优先匹配长关键词（避免短词误匹配）
            self.keyword_cache.sort(key=lambda x: x["keyword_len"], reverse=True)

        # 性能日志
        if ENABLE_DEBUG_LOGS:
            self._log.info(f"缓存更新完成: {len(self.monitored_group_ids)}个监控群, {len(self.keyword_cache)}个关键词")

    def run(self):
        self.bot.run()

def bot_run(bot_uin=None):
    # 如果未指定QQ号，尝试从配置文件中获取
    if bot_uin is None:
        bot_config = config_manager.load_config()
        bot_uin = bot_config.get("bot_uin", "382435027")
        
    robot = QQrobot(bot_uin)
    robot.run()

# ========== 启动 BotClient==========
if __name__ == "__main__":
    bot_run()