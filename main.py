# ========= 导入必要模块 ==========
from ncatbot.core import BotClient, GroupMessage, PrivateMessage
from ncatbot.utils import get_log
from ncatbot.utils import config
import config_manager
import os
import asyncio

# 性能优化配置 - 从环境变量或配置文件读取
ENABLE_DEBUG_LOGS = os.getenv("DISABLE_DEBUG_LOGS") != "1"
ENABLE_TIMING_LOGS = False  # 生产环境建议关闭
FORCE_CACHE_MODE = os.getenv("FORCE_CACHE_MODE") == "1"
ENABLE_ULTRA_MODE = os.getenv("ENABLE_ULTRA_MODE") == "1"  # 终极性能模式

class QQrobot:
    def __init__(self, bot_uin="382435027"):
        config.set_bot_uin(bot_uin)  # 设置 bot qq 号 (必填)

        config.set_ws_uri("ws://localhost:3001")  # 设置 napcat websocket server 地址
        config.set_ws_token("")  # 设置 token (websocket 的 token)
        config.set_webui_uri("http://localhost:6099")  # 设置 napcat webui 地址
        config.set_webui_token("napcat")  # 设置 token (webui 的 token)

        # ========== 创建 BotClient ==========
        self.bot = BotClient()
        self._log = get_log()

        # 加载配置文件并缓存监控群ID集合和关键词
        self.bot_config = config_manager.load_config()
        self._update_caches()

        # ========= 注册回调函数 ==========
        @self.bot.group_event()
        async def on_group_message(msg: GroupMessage):
            # 极速过滤：立即检查群ID（避免任何不必要的操作）
            if msg.group_id not in self.monitored_group_ids:
                return

            # 极速过滤：检查消息内容
            message = msg.raw_message
            if not message or len(message) > 200:  # 跳过空消息和超长消息
                return

            # 使用优化的关键词匹配算法
            matched_reply = self._fast_keyword_match(message)
            if matched_reply:
                # 使用异步任务发送回复，避免阻塞
                self._schedule_reply(msg, matched_reply)

                # 极简日志（仅在必要时）
                if ENABLE_DEBUG_LOGS:
                    self._log.info(f"群{msg.group_id}匹配")

    def _fast_keyword_match(self, message):
        """优化的关键词匹配算法"""
        # 使用字符串查找优化
        message_lower = message.lower()  # 预处理一次

        # 优先检查短关键词（通常匹配概率更高）
        for keyword_data in self.keyword_cache:
            keyword = keyword_data["keyword"]
            if len(keyword) <= 3:  # 短关键词优先
                if keyword in message_lower:
                    return keyword_data["reply"]

        # 再检查长关键词
        for keyword_data in self.keyword_cache:
            keyword = keyword_data["keyword"]
            if len(keyword) > 3:
                if keyword in message_lower:
                    return keyword_data["reply"]

        return None

    def _schedule_reply(self, msg, reply_text):
        """异步调度回复，避免阻塞主处理流程"""
        import asyncio

        async def send_reply():
            try:
                await msg.reply(text=reply_text)
            except Exception as e:
                if ENABLE_DEBUG_LOGS:
                    self._log.error(f"回复失败: {e}")

        # 创建异步任务，不等待完成
        asyncio.create_task(send_reply())

        @self.bot.private_event()
        async def on_private_message(msg: PrivateMessage):
            self._log.info(msg)
            if msg.raw_message == "测试":
                await self.bot.api.post_private_msg(msg.user_id, text="NcatBot 测试成功喵~")
            elif msg.raw_message == "重载配置":
                self.bot_config = config_manager.load_config()
                self._update_caches()  # 重载配置后更新所有缓存
                await self.bot.api.post_private_msg(msg.user_id, text="配置重载成功！")

    def _update_caches(self):
        """更新所有缓存，极致性能优化"""
        # 缓存监控群ID（使用int类型，避免字符串转换）
        self.monitored_group_ids = {int(group["group_id"]) for group in self.bot_config["monitored_groups"]}

        # 极致关键词缓存策略
        self.keyword_cache = []
        keyword_replies = self.bot_config["keyword_replies"]

        if keyword_replies:
            import random

            # 分类缓存：短关键词和长关键词分开存储
            short_keywords = []
            long_keywords = []

            for keyword, replies in keyword_replies.items():
                if replies:  # 只缓存有回复的关键词
                    # 预处理关键词（转小写，提高匹配效率）
                    processed_keyword = keyword.lower().strip()
                    if not processed_keyword:
                        continue

                    # 预选多个回复，支持轮换（避免回复单调）
                    selected_replies = replies[:3] if len(replies) >= 3 else replies
                    selected_reply = random.choice(selected_replies)

                    keyword_data = {
                        "keyword": processed_keyword,
                        "reply": selected_reply,
                        "keyword_len": len(processed_keyword),
                        "original_keyword": keyword  # 保留原始关键词用于日志
                    }

                    # 按长度分类存储
                    if len(processed_keyword) <= 3:
                        short_keywords.append(keyword_data)
                    else:
                        long_keywords.append(keyword_data)

            # 短关键词按使用频率排序（常用词在前）
            # 这里简化为按字母顺序，实际可以根据统计数据优化
            short_keywords.sort(key=lambda x: x["keyword"])

            # 长关键词按长度排序（长词优先，避免误匹配）
            long_keywords.sort(key=lambda x: x["keyword_len"], reverse=True)

            # 合并缓存：短关键词在前（匹配概率高）
            self.keyword_cache = short_keywords + long_keywords

        # 性能统计
        if ENABLE_DEBUG_LOGS:
            short_count = len([k for k in self.keyword_cache if k["keyword_len"] <= 3])
            long_count = len(self.keyword_cache) - short_count
            self._log.info(f"缓存更新: {len(self.monitored_group_ids)}群, {short_count}短词, {long_count}长词")

    def run(self):
        self.bot.run()

def bot_run(bot_uin=None):
    # 如果未指定QQ号，尝试从配置文件中获取
    if bot_uin is None:
        bot_config = config_manager.load_config()
        bot_uin = bot_config.get("bot_uin", "382435027")
        
    robot = QQrobot(bot_uin)
    robot.run()

# ========== 启动 BotClient==========
if __name__ == "__main__":
    bot_run()