import tkinter as tk
from tkinter import messagebox, simpledialog
from ttkbootstrap import Style, ttk
import random
import threading
import json
import subprocess
import os
from get_grouplist import get_group_list
import config_manager
from main import bot_run

class BotGUI:
    def __init__(self, root):
        self.root = root
        
        # 应用ttkbootstrap样式
        self.style = Style(theme="cosmo")  # 可选主题：cosmo, flatly, litera, minty, lumen, sandstone, yeti等
        
        self.root.title("QQ群监控机器人")
        self.root.geometry("1000x600")
        
        # 加载配置
        self.config = config_manager.load_config()
        
        # QQ号 - 从配置中读取，如果配置中没有则使用默认值
        self.bot_uin = tk.StringVar(value=self.config.get("bot_uin", "2423693600"))
        
        # 创建主框架
        self.create_widgets()
        
        # 注释掉自动加载群列表
        # self.load_group_list()
        
        # 加载监控群
        self.load_monitored_groups()
        
        # 加载关键词回复
        self.load_keyword_replies()
        
        # 机器人进程
        self.bot_process = None
    
    def create_widgets(self):
        # 创建顶部按钮栏
        top_frame = ttk.Frame(self.root)
        top_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 添加QQ号设置组件
        ttk.Label(top_frame, text="机器人QQ号:").pack(side=tk.LEFT, padx=5)
        qq_entry = ttk.Entry(top_frame, textvariable=self.bot_uin, width=15)
        qq_entry.pack(side=tk.LEFT, padx=5)
        
        # 启动机器人按钮
        self.start_bot_btn = ttk.Button(top_frame, text="启动机器人", command=self.start_bot, style="success.TButton")
        self.start_bot_btn.pack(side=tk.LEFT, padx=5)
        
        # 停止机器人按钮
        self.stop_bot_btn = ttk.Button(top_frame, text="停止机器人", command=self.stop_bot, state=tk.DISABLED, style="danger.TButton")
        self.stop_bot_btn.pack(side=tk.LEFT, padx=5)

        # 刷新配置按钮
        ttk.Button(top_frame, text="刷新配置", command=self.refresh_config, style="info.TButton").pack(side=tk.LEFT, padx=5)
        
        # 创建主分隔面板
        self.paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧群列表面板
        self.left_frame = ttk.LabelFrame(self.paned, text="QQ群列表")
        self.paned.add(self.left_frame, weight=1)
        
        # 群列表标签和刷新按钮容器
        group_label_frame = ttk.Frame(self.left_frame)
        group_label_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(group_label_frame, text="刷新群列表", command=self.load_group_list, style="info.TButton").pack(side=tk.RIGHT)
        
        # 群列表树视图
        self.group_tree = ttk.Treeview(self.left_frame, columns=("群名称", "群号"), show="headings")
        self.group_tree.heading("群名称", text="群名称")
        self.group_tree.heading("群号", text="群号")
        self.group_tree.column("群名称", width=150)
        self.group_tree.column("群号", width=120)
        self.group_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        group_scroll = ttk.Scrollbar(self.left_frame, orient="vertical", command=self.group_tree.yview)
        group_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.group_tree.configure(yscrollcommand=group_scroll.set)
        
        # 添加到监控按钮
        ttk.Button(self.left_frame, text="添加到监控列表", command=self.add_to_monitor, style="primary.TButton").pack(padx=5, pady=5)
        
        # 中间监控群面板
        self.middle_frame = ttk.LabelFrame(self.paned, text="监控群列表")
        self.paned.add(self.middle_frame, weight=1)
        
        # 监控群树视图
        self.monitor_tree = ttk.Treeview(self.middle_frame, columns=("群名称", "群号"), show="headings")
        self.monitor_tree.heading("群名称", text="群名称")
        self.monitor_tree.heading("群号", text="群号")
        self.monitor_tree.column("群名称", width=150)
        self.monitor_tree.column("群号", width=120)
        self.monitor_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        monitor_scroll = ttk.Scrollbar(self.middle_frame, orient="vertical", command=self.monitor_tree.yview)
        monitor_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.monitor_tree.configure(yscrollcommand=monitor_scroll.set)
        
        # 移除监控按钮
        ttk.Button(self.middle_frame, text="从监控列表移除", command=self.remove_from_monitor, style="warning.TButton").pack(padx=5, pady=5)
        
        # 右侧关键词面板
        self.right_frame = ttk.LabelFrame(self.paned, text="关键词回复设置")
        self.paned.add(self.right_frame, weight=2)
        
        # 关键词回复表格
        self.create_keyword_reply_table()
    
    def create_keyword_reply_table(self):
        """创建关键词回复表格"""
        # 表格框架
        table_frame = ttk.Frame(self.right_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建表格
        columns = ("关键词", "回复内容数量")
        self.keyword_table = ttk.Treeview(table_frame, columns=columns, show="headings")
        
        # 设置列标题
        for col in columns:
            self.keyword_table.heading(col, text=col)
            self.keyword_table.column(col, width=100)
        
        # 添加滚动条
        y_scroll = ttk.Scrollbar(table_frame, orient="vertical", command=self.keyword_table.yview)
        y_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.keyword_table.configure(yscrollcommand=y_scroll.set)
        self.keyword_table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 按钮框架
        btn_frame = ttk.Frame(self.right_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(btn_frame, text="添加关键词", command=self.add_keyword, style="primary.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="编辑关键词", command=self.edit_keyword, style="info.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="删除关键词", command=self.delete_keyword, style="danger.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="管理回复", command=self.manage_replies, style="success.TButton").pack(side=tk.LEFT, padx=5)
        
        # 绑定双击事件
        self.keyword_table.bind("<Double-1>", lambda e: self.manage_replies())
    
    def load_group_list(self):
        """加载群列表（异步处理，避免界面卡顿）"""
        def load_in_thread():
            try:
                groups = get_group_list()
                # 在主线程中更新UI
                self.root.after(0, lambda: self._update_group_list_ui(groups))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"加载群列表失败: {e}"))

        # 在后台线程中加载群列表
        threading.Thread(target=load_in_thread, daemon=True).start()

    def _update_group_list_ui(self, groups):
        """在主线程中更新群列表UI"""
        # 清空现有群列表
        for item in self.group_tree.get_children():
            self.group_tree.delete(item)

        # 批量插入数据
        for name, gid in groups.items():
            self.group_tree.insert("", tk.END, values=(name, gid))

        messagebox.showinfo("提示", "群列表加载成功")
    
    def load_monitored_groups(self):
        """加载监控群列表"""
        # 清空现有监控群列表
        for item in self.monitor_tree.get_children():
            self.monitor_tree.delete(item)
        
        # 从配置中加载监控群
        for group in self.config["monitored_groups"]:
            self.monitor_tree.insert("", tk.END, values=(group["group_name"], group["group_id"]))
    
    def load_keyword_replies(self):
        """加载关键词回复表格"""
        # 清空现有关键词表格
        for item in self.keyword_table.get_children():
            self.keyword_table.delete(item)
        
        # 从配置中加载关键词和回复数量
        for keyword, replies in self.config["keyword_replies"].items():
            self.keyword_table.insert("", tk.END, values=(keyword, len(replies)))
    
    def add_to_monitor(self):
        """添加选中群到监控列表（优化：批量操作和缓存检查）"""
        selected = self.group_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择要监控的群")
            return

        # 获取当前监控群ID集合，避免重复遍历
        monitored_ids = {self.monitor_tree.item(item, "values")[1]
                        for item in self.monitor_tree.get_children()}

        added_count = 0
        for item in selected:
            values = self.group_tree.item(item, "values")
            group_name, group_id = values

            # 快速检查是否已经在监控列表中
            if group_id not in monitored_ids:
                # 添加到监控列表
                if config_manager.add_monitored_group(self.config, group_id, group_name):
                    self.monitor_tree.insert("", tk.END, values=(group_name, group_id))
                    monitored_ids.add(group_id)
                    added_count += 1

        if added_count > 0:
            messagebox.showinfo("提示", f"成功添加 {added_count} 个群到监控列表")
    
    def remove_from_monitor(self):
        """从监控列表中移除选中群"""
        selected = self.monitor_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择要移除的监控群")
            return
        
        for item in selected:
            values = self.monitor_tree.item(item, "values")
            group_id = values[1]
            
            # 从配置中移除
            config_manager.remove_monitored_group(self.config, group_id)
            
            # 从树视图中移除
            self.monitor_tree.delete(item)
    
    def add_keyword(self):
        """添加关键词"""
        keyword = simpledialog.askstring("添加关键词", "请输入关键词:")
        if not keyword:
            return
        
        # 检查是否已存在
        for item in self.keyword_table.get_children():
            if self.keyword_table.item(item, "values")[0] == keyword:
                messagebox.showwarning("警告", "该关键词已存在")
                return
        
        # 添加到关键词列表
        if keyword not in self.config["keyword_replies"]:
            self.config["keyword_replies"][keyword] = []
            config_manager.save_config(self.config)
            
        self.keyword_table.insert("", tk.END, values=(keyword, 0))
        
        # 弹出回复管理窗口
        self.manage_replies_for_keyword(keyword)
    
    def edit_keyword(self):
        """编辑关键词"""
        selected = self.keyword_table.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择要编辑的关键词")
            return
        
        old_keyword = self.keyword_table.item(selected[0], "values")[0]
        new_keyword = simpledialog.askstring("编辑关键词", "请输入新的关键词:", initialvalue=old_keyword)
        
        if not new_keyword or new_keyword == old_keyword:
            return
        
        # 检查新关键词是否已存在
        for item in self.keyword_table.get_children():
            if item != selected[0] and self.keyword_table.item(item, "values")[0] == new_keyword:
                messagebox.showwarning("警告", "该关键词已存在")
                return
        
        # 更新配置
        replies = self.config["keyword_replies"].pop(old_keyword)
        self.config["keyword_replies"][new_keyword] = replies
        config_manager.save_config(self.config)
        
        # 更新表格
        self.keyword_table.item(selected[0], values=(new_keyword, len(replies)))
    
    def delete_keyword(self):
        """删除关键词"""
        selected = self.keyword_table.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择要删除的关键词")
            return
        
        if not messagebox.askyesno("确认", "确定要删除选中的关键词及其所有回复内容吗?"):
            return
        
        for item in selected:
            keyword = self.keyword_table.item(item, "values")[0]
            
            # 从配置中删除
            config_manager.remove_keyword_reply(self.config, keyword)
            
            # 从表格中删除
            self.keyword_table.delete(item)
    
    def manage_replies(self):
        """管理回复内容"""
        selected = self.keyword_table.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择要管理回复的关键词")
            return
        
        keyword = self.keyword_table.item(selected[0], "values")[0]
        self.manage_replies_for_keyword(keyword)
    
    def manage_replies_for_keyword(self, keyword):
        """为指定关键词管理回复内容"""
        # 创建回复管理窗口
        reply_window = tk.Toplevel(self.root)
        reply_window.title(f"管理关键词 '{keyword}' 的回复")
        reply_window.geometry("500x400")
        reply_window.transient(self.root)  # 设置为主窗口的子窗口
        reply_window.grab_set()  # 模态窗口
        
        # 回复列表框架
        list_frame = ttk.Frame(reply_window)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 回复列表
        columns = ("序号", "回复内容")
        reply_tree = ttk.Treeview(list_frame, columns=columns, show="headings")
        
        # 设置列标题
        reply_tree.heading("序号", text="序号")
        reply_tree.heading("回复内容", text="回复内容")
        reply_tree.column("序号", width=50)
        reply_tree.column("回复内容", width=400)
        
        # 添加滚动条
        y_scroll = ttk.Scrollbar(list_frame, orient="vertical", command=reply_tree.yview)
        y_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        reply_tree.configure(yscrollcommand=y_scroll.set)
        reply_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 加载回复内容
        replies = self.config["keyword_replies"].get(keyword, [])
        for i, reply in enumerate(replies, 1):
            reply_tree.insert("", tk.END, values=(i, reply))
        
        # 按钮框架
        btn_frame = ttk.Frame(reply_window)
        btn_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 添加回复按钮
        def add_reply():
            reply = simpledialog.askstring("添加回复", "请输入回复内容:")
            if not reply:
                return
            
            # 检查是否已存在
            for item in reply_tree.get_children():
                if reply_tree.item(item, "values")[1] == reply:
                    messagebox.showwarning("警告", "该回复内容已存在")
                    return
            
            # 添加到配置
            config_manager.add_keyword_reply(self.config, keyword, reply)
            
            # 更新表格
            count = len(reply_tree.get_children()) + 1
            reply_tree.insert("", tk.END, values=(count, reply))
            
            # 更新主表格中的回复数量
            for item in self.keyword_table.get_children():
                if self.keyword_table.item(item, "values")[0] == keyword:
                    self.keyword_table.item(item, values=(keyword, count))
                    break
        
        # 编辑回复按钮
        def edit_reply():
            selected = reply_tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请先选择要编辑的回复")
                return
            
            old_reply = reply_tree.item(selected[0], "values")[1]
            new_reply = simpledialog.askstring("编辑回复", "请输入新的回复内容:", initialvalue=old_reply)
            
            if not new_reply or new_reply == old_reply:
                return
            
            # 检查新回复是否已存在
            for item in reply_tree.get_children():
                if item != selected[0] and reply_tree.item(item, "values")[1] == new_reply:
                    messagebox.showwarning("警告", "该回复内容已存在")
                    return
            
            # 更新配置
            replies = self.config["keyword_replies"][keyword]
            index = replies.index(old_reply)
            replies[index] = new_reply
            config_manager.save_config(self.config)
            
            # 更新表格
            reply_tree.item(selected[0], values=(reply_tree.item(selected[0], "values")[0], new_reply))
        
        # 删除回复按钮
        def delete_reply():
            selected = reply_tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请先选择要删除的回复")
                return
            
            reply = reply_tree.item(selected[0], "values")[1]
            
            # 从配置中删除
            config_manager.remove_keyword_reply(self.config, keyword, reply)
            
            # 从表格中删除
            reply_tree.delete(selected[0])
            
            # 重新排序
            for i, item in enumerate(reply_tree.get_children(), 1):
                reply_tree.item(item, values=(i, reply_tree.item(item, "values")[1]))
            
            # 更新主表格中的回复数量
            new_count = len(reply_tree.get_children())
            for item in self.keyword_table.get_children():
                if self.keyword_table.item(item, "values")[0] == keyword:
                    self.keyword_table.item(item, values=(keyword, new_count))
                    break
        
        ttk.Button(btn_frame, text="添加回复", command=add_reply).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="编辑回复", command=edit_reply).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="删除回复", command=delete_reply).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="关闭", command=reply_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def start_bot(self):
        """启动机器人（优化：快速响应和错误处理）"""
        # 创建一个标志来跟踪机器人是否正在运行
        if hasattr(self, 'is_bot_running') and self.is_bot_running:
            messagebox.showinfo("提示", "机器人已经在运行中")
            return

        # 获取用户设置的QQ号
        bot_uin = self.bot_uin.get().strip()
        if not bot_uin:
            messagebox.showwarning("警告", "请输入机器人QQ号")
            return

        # 验证QQ号格式
        if not bot_uin.isdigit() or len(bot_uin) < 5:
            messagebox.showwarning("警告", "请输入有效的QQ号")
            return

        try:
            # 立即更新UI状态，提供快速反馈
            self.start_bot_btn.config(state=tk.DISABLED, text="启动中...")
            self.root.update_idletasks()

            # 保存QQ号到配置
            config_manager.set_bot_uin(self.config, bot_uin)

            # 设置机器人运行标志
            self.is_bot_running = True

            # 启动机器人进程,单独放一个线程，使用用户设置的QQ号
            self.bot_thread = threading.Thread(target=lambda: bot_run(bot_uin=bot_uin))
            self.bot_thread.daemon = True
            self.bot_thread.start()

            # 更新按钮状态
            self.start_bot_btn.config(text="启动机器人")
            self.stop_bot_btn.config(state=tk.NORMAL)

            messagebox.showinfo("提示", f"机器人已启动，QQ号: {bot_uin}")

        except Exception as e:
            # 恢复按钮状态
            self.start_bot_btn.config(state=tk.NORMAL, text="启动机器人")
            self.is_bot_running = False
            messagebox.showerror("错误", f"启动机器人失败: {e}")
    
    def stop_bot(self):
        """停止机器人（优化：改进停止逻辑）"""
        if not hasattr(self, 'is_bot_running') or not self.is_bot_running:
            messagebox.showinfo("提示", "机器人未在运行")
            return

        try:
            # 立即更新UI状态
            self.stop_bot_btn.config(state=tk.DISABLED, text="停止中...")
            self.root.update_idletasks()

            # 设置停止标志
            self.is_bot_running = False

            # 注意：由于使用的是线程而不是进程，这里主要是设置标志
            # 实际的机器人停止需要在bot框架层面实现

            # 更新按钮状态
            self.start_bot_btn.config(state=tk.NORMAL)
            self.stop_bot_btn.config(state=tk.DISABLED, text="停止机器人")

            messagebox.showinfo("提示", "机器人停止信号已发送")

        except Exception as e:
            messagebox.showerror("错误", f"停止机器人失败: {e}")
            # 恢复按钮状态
            self.stop_bot_btn.config(state=tk.NORMAL, text="停止机器人")
    
    def refresh_config(self):
        """刷新配置缓存（提供手动刷新选项）"""
        try:
            # 清理配置缓存，强制重新加载
            config_manager.clear_cache()

            self.config = config_manager.load_config()
            self.load_monitored_groups()
            self.load_keyword_replies()

            messagebox.showinfo("提示", "配置已刷新")
        except Exception as e:
            messagebox.showerror("错误", f"刷新配置失败: {e}")

def main():
    root = tk.Tk()
    app = BotGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main() 