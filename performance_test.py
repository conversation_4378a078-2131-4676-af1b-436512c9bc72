#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能测试脚本 - 测试QQ机器人回复速度优化效果
"""

import time
import random
import config_manager
from main import QQrobot

class MockMessage:
    """模拟消息对象"""
    def __init__(self, group_id, raw_message):
        self.group_id = group_id
        self.raw_message = raw_message
    
    async def reply(self, text):
        """模拟回复"""
        pass

def test_group_check_performance():
    """测试群检查性能"""
    print("=== 群检查性能测试 ===")
    
    # 创建机器人实例
    robot = QQrobot()
    
    # 测试数据
    monitored_group = 666732394
    non_monitored_group = 999999999
    test_count = 100000
    
    # 测试监控群检查
    start_time = time.time()
    for _ in range(test_count):
        result = monitored_group in robot.monitored_group_ids
    end_time = time.time()
    
    print(f"监控群检查 {test_count} 次耗时: {(end_time - start_time)*1000:.2f}ms")
    print(f"平均每次检查耗时: {(end_time - start_time)*1000000/test_count:.2f}μs")
    
    # 测试非监控群检查
    start_time = time.time()
    for _ in range(test_count):
        result = non_monitored_group in robot.monitored_group_ids
    end_time = time.time()
    
    print(f"非监控群检查 {test_count} 次耗时: {(end_time - start_time)*1000:.2f}ms")
    print(f"平均每次检查耗时: {(end_time - start_time)*1000000/test_count:.2f}μs")

def test_keyword_matching_performance():
    """测试关键词匹配性能"""
    print("\n=== 关键词匹配性能测试 ===")
    
    # 创建机器人实例
    robot = QQrobot()
    
    # 测试数据
    test_messages = [
        "你好，请问有什么可以帮助的吗？",
        "这是一条不包含关键词的消息",
        "你好世界",
        "hello world",
        "测试消息内容"
    ]
    
    test_count = 10000
    
    # 测试关键词匹配
    start_time = time.time()
    match_count = 0
    
    for _ in range(test_count):
        message = random.choice(test_messages)
        for keyword_data in robot.keyword_cache:
            if keyword_data["keyword"] in message:
                match_count += 1
                break
    
    end_time = time.time()
    
    print(f"关键词匹配测试 {test_count} 次耗时: {(end_time - start_time)*1000:.2f}ms")
    print(f"平均每次匹配耗时: {(end_time - start_time)*1000000/test_count:.2f}μs")
    print(f"匹配成功次数: {match_count}")

def test_config_loading_performance():
    """测试配置加载性能"""
    print("\n=== 配置加载性能测试 ===")
    
    test_count = 1000
    
    # 测试缓存命中性能
    start_time = time.time()
    for _ in range(test_count):
        config = config_manager.load_config()
    end_time = time.time()
    
    print(f"配置加载（缓存命中）{test_count} 次耗时: {(end_time - start_time)*1000:.2f}ms")
    print(f"平均每次加载耗时: {(end_time - start_time)*1000000/test_count:.2f}μs")

def test_end_to_end_performance():
    """端到端性能测试"""
    print("\n=== 端到端性能测试 ===")
    
    robot = QQrobot()
    
    # 模拟消息处理流程
    test_messages = [
        MockMessage(666732394, "你好，请问有什么可以帮助的吗？"),
        MockMessage(666732394, "这是一条不包含关键词的消息"),
        MockMessage(999999999, "你好世界"),  # 非监控群
        MockMessage(666732394, "你好"),
    ]
    
    test_count = 1000
    
    async def process_message(msg):
        """模拟消息处理逻辑"""
        # 快速群检查
        if msg.group_id not in robot.monitored_group_ids:
            return False
        
        # 快速关键词匹配
        message = msg.raw_message
        if not message:
            return False
            
        for keyword_data in robot.keyword_cache:
            if keyword_data["keyword"] in message:
                await msg.reply(keyword_data["reply"])
                return True
        return False
    
    # 同步版本的测试
    def sync_process_message(msg):
        """同步版本的消息处理"""
        if msg.group_id not in robot.monitored_group_ids:
            return False
        
        message = msg.raw_message
        if not message:
            return False
            
        for keyword_data in robot.keyword_cache:
            if keyword_data["keyword"] in message:
                return True
        return False
    
    start_time = time.time()
    processed_count = 0
    
    for _ in range(test_count):
        msg = random.choice(test_messages)
        if sync_process_message(msg):
            processed_count += 1
    
    end_time = time.time()
    
    print(f"端到端处理 {test_count} 条消息耗时: {(end_time - start_time)*1000:.2f}ms")
    print(f"平均每条消息处理耗时: {(end_time - start_time)*1000000/test_count:.2f}μs")
    print(f"成功处理消息数: {processed_count}")

def main():
    """运行所有性能测试"""
    print("QQ机器人性能测试开始...")
    print("=" * 50)
    
    test_group_check_performance()
    test_keyword_matching_performance()
    test_config_loading_performance()
    test_end_to_end_performance()
    
    print("\n" + "=" * 50)
    print("性能测试完成！")
    
    print("\n性能优化建议:")
    print("1. 如果群检查耗时超过1μs，考虑优化群ID缓存")
    print("2. 如果关键词匹配耗时超过10μs，考虑使用更高效的字符串匹配算法")
    print("3. 如果配置加载耗时超过100μs，检查缓存是否正常工作")
    print("4. 端到端处理应该控制在50μs以内以保证良好的响应速度")

if __name__ == "__main__":
    main()
