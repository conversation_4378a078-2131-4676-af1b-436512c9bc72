#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高性能启动脚本 - 应用所有性能优化设置启动QQ机器人
"""

import os
import sys
import time

def apply_performance_settings():
    """应用性能优化设置"""
    print("正在应用性能优化设置...")
    
    # 设置环境变量以禁用调试日志
    os.environ["DISABLE_DEBUG_LOGS"] = "1"
    os.environ["FORCE_CACHE_MODE"] = "1"
    
    # Python优化设置
    os.environ["PYTHONOPTIMIZE"] = "1"  # 启用Python优化
    os.environ["PYTHONDONTWRITEBYTECODE"] = "1"  # 不生成.pyc文件
    
    print("✅ 性能优化设置已应用")

def check_dependencies():
    """检查依赖项"""
    print("正在检查依赖项...")
    
    required_modules = [
        "ncatbot",
        "tkinter",
        "ttkbootstrap"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少依赖项: {', '.join(missing_modules)}")
        return False
    
    print("✅ 所有依赖项检查通过")
    return True

def optimize_config():
    """优化配置文件"""
    print("正在优化配置...")
    
    try:
        import config_manager
        
        # 预热配置缓存
        config = config_manager.load_config()
        
        # 验证配置完整性
        required_keys = ["bot_uin", "monitored_groups", "keyword_replies"]
        for key in required_keys:
            if key not in config:
                print(f"❌ 配置文件缺少必要字段: {key}")
                return False
        
        print(f"✅ 配置优化完成 - 监控{len(config['monitored_groups'])}个群，{len(config['keyword_replies'])}个关键词")
        return True
        
    except Exception as e:
        print(f"❌ 配置优化失败: {e}")
        return False

def start_bot_optimized():
    """启动优化版机器人"""
    print("正在启动高性能QQ机器人...")
    
    try:
        from main import bot_run
        import config_manager
        
        # 获取配置中的QQ号
        config = config_manager.load_config()
        bot_uin = config.get("bot_uin", "3292105955")
        
        print(f"🚀 启动机器人，QQ号: {bot_uin}")
        
        # 记录启动时间
        start_time = time.time()
        
        # 启动机器人
        bot_run(bot_uin=bot_uin)
        
    except KeyboardInterrupt:
        print("\n⏹️ 机器人已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def start_gui_optimized():
    """启动优化版GUI"""
    print("正在启动高性能GUI界面...")
    
    try:
        from gui import main as gui_main
        
        print("🖥️ 启动GUI界面")
        gui_main()
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        return False
    
    return True

def run_performance_test():
    """运行性能测试"""
    print("正在运行性能测试...")
    
    try:
        import performance_test
        performance_test.main()
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

def show_performance_tips():
    """显示性能优化提示"""
    tips = [
        "🔧 性能优化提示:",
        "1. 确保关闭不必要的日志记录",
        "2. 定期清理配置缓存",
        "3. 监控群数量建议控制在50个以内",
        "4. 关键词数量建议控制在100个以内",
        "5. 避免使用过长的关键词",
        "6. 定期重启机器人以清理内存",
        "7. 使用SSD硬盘可提升配置加载速度",
        "8. 确保网络连接稳定"
    ]
    
    for tip in tips:
        print(tip)

def main():
    """主函数"""
    print("=" * 60)
    print("🤖 QQ机器人高性能启动器")
    print("=" * 60)
    
    # 应用性能设置
    apply_performance_settings()
    
    # 检查依赖
    if not check_dependencies():
        print("请安装缺少的依赖项后重试")
        return
    
    # 优化配置
    if not optimize_config():
        print("配置优化失败，请检查配置文件")
        return
    
    # 显示性能提示
    show_performance_tips()
    
    print("\n请选择启动模式:")
    print("1. 启动机器人 (命令行模式)")
    print("2. 启动GUI界面")
    print("3. 运行性能测试")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == "1":
                start_bot_optimized()
                break
            elif choice == "2":
                start_gui_optimized()
                break
            elif choice == "3":
                run_performance_test()
                break
            elif choice == "4":
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入1-4")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
