#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
网络层优化模块 - 针对WebSocket连接和HTTP请求的性能优化
"""

import json
import os
import shutil

def optimize_napcat_config():
    """优化NapCat配置以获得最佳性能"""
    
    # 高性能WebSocket配置
    optimized_config = {
        "network": {
            "websocketServers": [
                {
                    "name": "WsServer",
                    "enable": True,
                    "host": "127.0.0.1",  # 使用本地回环，减少网络延迟
                    "port": 3001,
                    "messagePostFormat": "array",
                    "reportSelfMessage": False,
                    "token": "",
                    "enableForcePushEvent": True,
                    "debug": False,  # 关闭调试以提升性能
                    "heartInterval": 10000,  # 减少心跳间隔，提高响应速度
                    "maxConnections": 100,  # 增加最大连接数
                    "compression": True,  # 启用压缩
                    "perMessageDeflate": True,  # 启用消息压缩
                    "maxPayload": 1048576,  # 1MB最大负载
                    "pingTimeout": 5000,  # 5秒ping超时
                    "pongTimeout": 3000,  # 3秒pong超时
                }
            ],
            "httpServers": [
                {
                    "name": "HttpServer", 
                    "enable": False,  # 禁用HTTP服务器，只使用WebSocket
                    "host": "127.0.0.1",
                    "port": 3000,
                    "enableCors": False,
                    "enableWebsocket": False,
                    "messagePostFormat": "array",
                    "token": "",
                    "debug": False
                }
            ]
        },
        "musicSignUrl": "",
        "enableLocalFile2Url": False,
        "parseMultMsg": False,
        "enableQQNTIM": True,
        "reportSelfMessage": False,
        "enableForcePushEvent": True,
        
        # 性能优化配置
        "performance": {
            "enableMessageQueue": True,
            "messageQueueSize": 1000,
            "enableBatchSend": True,
            "batchSize": 10,
            "batchTimeout": 100,  # 100ms批处理超时
            "enableConnectionPool": True,
            "connectionPoolSize": 20,
            "enableKeepAlive": True,
            "keepAliveTimeout": 30000,
            "enableCompression": True,
            "compressionLevel": 6,
            "enableCache": True,
            "cacheSize": 1000,
            "cacheTTL": 300000  # 5分钟缓存TTL
        }
    }
    
    return optimized_config

def apply_napcat_optimizations():
    """应用NapCat性能优化配置"""
    
    config_paths = [
        "napcat/config/onebot11_1420937534.json",
        "versions/9.9.19-34740/resources/app/napcat/config/onebot11_1420937534.json"
    ]
    
    optimized_config = optimize_napcat_config()
    
    for config_path in config_paths:
        if os.path.exists(config_path):
            try:
                # 备份原配置
                backup_path = config_path + ".backup"
                if not os.path.exists(backup_path):
                    shutil.copy2(config_path, backup_path)
                
                # 写入优化配置
                with open(config_path, "w", encoding="utf-8") as f:
                    json.dump(optimized_config, f, ensure_ascii=False, indent=2)
                
                print(f"✅ 已优化配置文件: {config_path}")
                
            except Exception as e:
                print(f"❌ 优化配置失败 {config_path}: {e}")

def create_performance_batch_script():
    """创建性能优化的批处理脚本"""
    
    batch_content = """@echo off
title QQ机器人高性能启动
echo 正在应用性能优化设置...

REM 设置环境变量
set DISABLE_DEBUG_LOGS=1
set FORCE_CACHE_MODE=1
set ENABLE_ULTRA_MODE=1
set PYTHONOPTIMIZE=1
set PYTHONDONTWRITEBYTECODE=1

REM 设置进程优先级为高
wmic process where name="python.exe" CALL setpriority "high priority"

REM 启动机器人
echo 启动高性能QQ机器人...
python start_optimized.py

pause
"""
    
    with open("start_ultra_performance.bat", "w", encoding="gbk") as f:
        f.write(batch_content)
    
    print("✅ 已创建高性能启动脚本: start_ultra_performance.bat")

def optimize_system_settings():
    """优化系统设置以获得最佳性能"""
    
    optimizations = [
        "1. 设置Python进程为高优先级",
        "2. 禁用Windows Defender实时保护（临时）",
        "3. 关闭不必要的后台程序",
        "4. 使用有线网络连接而非WiFi",
        "5. 确保充足的内存（建议8GB+）",
        "6. 使用SSD硬盘存储配置文件",
        "7. 关闭Windows更新自动下载",
        "8. 设置电源计划为高性能模式"
    ]
    
    print("🚀 系统性能优化建议:")
    for tip in optimizations:
        print(f"   {tip}")

def benchmark_network_latency():
    """测试网络延迟"""
    import time
    import socket
    
    print("\n🌐 网络延迟测试:")
    
    # 测试本地回环延迟
    try:
        start_time = time.time()
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(("127.0.0.1", 3001))
        sock.close()
        end_time = time.time()
        
        if result == 0:
            latency = (end_time - start_time) * 1000
            print(f"   本地WebSocket延迟: {latency:.2f}ms")
            
            if latency < 1:
                print("   ✅ 网络延迟优秀")
            elif latency < 5:
                print("   ✅ 网络延迟良好")
            elif latency < 20:
                print("   ⚠️ 网络延迟一般")
            else:
                print("   ❌ 网络延迟较高，建议检查网络配置")
        else:
            print("   ❌ 无法连接到WebSocket服务器")
            
    except Exception as e:
        print(f"   ❌ 网络测试失败: {e}")

def create_monitoring_script():
    """创建性能监控脚本"""
    
    monitoring_script = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

import psutil
import time
import os

def monitor_performance():
    """实时监控机器人性能"""
    print("🔍 QQ机器人性能监控器")
    print("=" * 50)
    
    while True:
        try:
            # 获取Python进程信息
            python_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info']):
                if 'python' in proc.info['name'].lower():
                    python_processes.append(proc)
            
            if python_processes:
                print(f"\\n时间: {time.strftime('%H:%M:%S')}")
                print("-" * 50)
                
                for proc in python_processes:
                    try:
                        cpu_percent = proc.cpu_percent()
                        memory_mb = proc.memory_info().rss / 1024 / 1024
                        
                        print(f"PID: {proc.pid}")
                        print(f"CPU使用率: {cpu_percent:.1f}%")
                        print(f"内存使用: {memory_mb:.1f}MB")
                        
                        # 性能警告
                        if cpu_percent > 80:
                            print("⚠️ CPU使用率过高!")
                        if memory_mb > 500:
                            print("⚠️ 内存使用过高!")
                        
                        print("-" * 30)
                        
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            
            time.sleep(5)  # 5秒更新一次
            
        except KeyboardInterrupt:
            print("\\n监控已停止")
            break
        except Exception as e:
            print(f"监控错误: {e}")
            time.sleep(1)

if __name__ == "__main__":
    monitor_performance()
'''
    
    with open("performance_monitor.py", "w", encoding="utf-8") as f:
        f.write(monitoring_script)
    
    print("✅ 已创建性能监控脚本: performance_monitor.py")

def main():
    """主函数"""
    print("🚀 QQ机器人网络层性能优化")
    print("=" * 50)
    
    # 应用NapCat优化
    apply_napcat_optimizations()
    
    # 创建性能脚本
    create_performance_batch_script()
    create_monitoring_script()
    
    # 网络延迟测试
    benchmark_network_latency()
    
    # 系统优化建议
    optimize_system_settings()
    
    print("\\n🎯 优化完成! 建议使用以下方式启动:")
    print("   1. 双击 start_ultra_performance.bat")
    print("   2. 或运行 python start_optimized.py")
    print("   3. 使用 python performance_monitor.py 监控性能")

if __name__ == "__main__":
    main()
